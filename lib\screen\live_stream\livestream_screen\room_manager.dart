import 'package:get/get.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';

/// Singleton class to manage ZEGO room states and prevent duplicate room logins
class RoomManager {
  static final RoomManager _instance = RoomManager._internal();
  factory RoomManager() => _instance;
  RoomManager._internal();

  // Track rooms currently being joined
  final Set<String> _roomsBeingJoined = <String>{};
  
  // Track rooms that have failed to join
  final Map<String, String> _roomErrors = <String, String>{};
  
  // Track active room controllers
  final Map<String, String> _activeRooms = <String, String>{}; // roomID -> controllerTag

  /// Check if a room is currently being joined
  bool isRoomBeingJoined(String roomID) {
    return _roomsBeingJoined.contains(roomID);
  }

  /// Mark a room as being joined
  void markRoomAsBeingJoined(String roomID) {
    Loggers.info('🔒 Marking room as being joined: $roomID');
    _roomsBeingJoined.add(roomID);
    _roomErrors.remove(roomID); // Clear any previous errors
  }

  /// Mark a room as successfully joined
  void markRoomAsJoined(String roomID, String controllerTag) {
    Loggers.info('✅ Room successfully joined: $roomID');
    _roomsBeingJoined.remove(roomID);
    _activeRooms[roomID] = controllerTag;
    _roomErrors.remove(roomID);
  }

  /// Mark a room as failed to join
  void markRoomAsFailed(String roomID, String error) {
    Loggers.error('❌ Room failed to join: $roomID - $error');
    _roomsBeingJoined.remove(roomID);
    _roomErrors[roomID] = error;
    _activeRooms.remove(roomID);
  }

  /// Get error message for a room
  String? getRoomError(String roomID) {
    return _roomErrors[roomID];
  }

  /// Check if a room has an active controller
  bool hasActiveController(String roomID) {
    final controllerTag = _activeRooms[roomID];
    if (controllerTag == null) return false;
    
    try {
      Get.find<LivestreamScreenController>(tag: controllerTag);
      return true;
    } catch (e) {
      // Controller no longer exists, clean up
      _activeRooms.remove(roomID);
      return false;
    }
  }

  /// Clean up room state when controller is disposed
  void cleanupRoom(String roomID) {
    Loggers.info('🧹 Cleaning up room state: $roomID');
    _roomsBeingJoined.remove(roomID);
    _activeRooms.remove(roomID);
    _roomErrors.remove(roomID);
  }

  /// Get current state summary for debugging
  Map<String, dynamic> getStateSummary() {
    return {
      'roomsBeingJoined': _roomsBeingJoined.toList(),
      'roomErrors': _roomErrors,
      'activeRooms': _activeRooms,
    };
  }

  /// Clear all state (for testing/debugging)
  void clearAllState() {
    Loggers.warning('🧹 Clearing all room manager state');
    _roomsBeingJoined.clear();
    _roomErrors.clear();
    _activeRooms.clear();
  }
}

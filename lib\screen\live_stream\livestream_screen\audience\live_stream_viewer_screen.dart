import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  String? _currentActiveRoomID;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize the first stream controller immediately
    _activateStreamAtIndex(_currentIndex);
  }

  @override
  void dispose() {
    // Clean up the currently active controller
    _deactivateCurrentStream();
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      Loggers.info('Switching from stream $_currentIndex to stream $index');

      // Deactivate the previous stream
      _deactivateCurrentStream();

      // Immediately activate the new stream - no delay needed
      _currentIndex = index;
      _activateStreamAtIndex(index);
    }
  }

  void _activateStreamAtIndex(int index) {
    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      if (roomID != null) {
        Loggers.info('Activating stream for room: $roomID');
        _currentActiveRoomID = roomID;

        try {
          // Check if controller already exists
          Get.find<LivestreamScreenController>(tag: roomID);
          Loggers.info('Controller already exists for room: $roomID');
        } catch (e) {
          // Controller doesn't exist, create it
          try {
            Get.put(
              LivestreamScreenController(livestream.obs, false),
              tag: roomID,
            );
            Loggers.info('Controller created and activated for room: $roomID');
          } catch (createError) {
            Loggers.error('Failed to create controller for room: $roomID, error: $createError');
          }
        }
      }
    }
  }

  void _deactivateCurrentStream() {
    if (_currentActiveRoomID != null) {
      Loggers.info('Deactivating stream for room: $_currentActiveRoomID');

      try {
        // Find and properly dispose the controller
        final controller = Get.find<LivestreamScreenController>(tag: _currentActiveRoomID);

        // Perform immediate cleanup for faster switching
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $_currentActiveRoomID: $error');
        });

        // Delete the controller immediately - don't wait for logout
        Get.delete<LivestreamScreenController>(tag: _currentActiveRoomID);
        Loggers.info('Controller disposed for room: $_currentActiveRoomID');
      } catch (e) {
        Loggers.warning('Controller not found for room: $_currentActiveRoomID, error: $e');
      }

      _currentActiveRoomID = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        final isActive = index == _currentIndex;
        final roomID = widget.livestreams[index].roomID;

        Loggers.info('Building LiveStreamAudienceScreen - Index: $index, Active: $isActive, RoomID: $roomID, CurrentIndex: $_currentIndex');

        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
          isActive: isActive,
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  String? _currentActiveRoomID;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize the first stream controller immediately
    _activateStreamAtIndex(_currentIndex);
  }

  @override
  void dispose() {
    // Clean up the currently active controller
    _deactivateCurrentStream();
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    Loggers.info('📱 Page changed event - Old index: $_currentIndex, New index: $index');

    if (_currentIndex != index) {
      Loggers.info('🔄 Switching from stream $_currentIndex to stream $index');

      // Deactivate the previous stream
      Loggers.info('🛑 Deactivating previous stream...');
      _deactivateCurrentStream();

      // Immediately activate the new stream - no delay needed
      Loggers.info('📝 Updating current index from $_currentIndex to $index');
      _currentIndex = index;

      Loggers.info('🚀 Activating new stream...');
      _activateStreamAtIndex(index);

      Loggers.info('✅ Stream switching completed');
    } else {
      Loggers.info('ℹ️ Page changed to same index, no action needed');
    }
  }

  void _activateStreamAtIndex(int index) {
    Loggers.info('🔄 _activateStreamAtIndex called with index: $index');

    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      Loggers.info('📋 Stream details - Index: $index, RoomID: $roomID, Host: ${livestream.hostUser?.username}');

      if (roomID != null) {
        Loggers.info('🚀 Activating stream for room: $roomID');
        _currentActiveRoomID = roomID;

        try {
          // Check if controller already exists
          final existingController = Get.find<LivestreamScreenController>(tag: roomID);
          Loggers.success('✅ Controller already exists for room: $roomID');
          Loggers.info('Controller state: ${existingController.toString()}');
        } catch (e) {
          // Controller doesn't exist, create it
          Loggers.info('🔨 Creating new controller for room: $roomID');
          try {
            final newController = Get.put(
              LivestreamScreenController(livestream.obs, false),
              tag: roomID,
            );
            Loggers.success('✅ Controller created and activated for room: $roomID');
            Loggers.info('New controller state: ${newController.toString()}');

            // Verify the controller can be found immediately after creation
            try {
              final verifyController = Get.find<LivestreamScreenController>(tag: roomID);
              Loggers.success('✅ Controller verification successful for room: $roomID');
            } catch (verifyError) {
              Loggers.error('❌ Controller verification failed for room: $roomID, error: $verifyError');
            }
          } catch (createError) {
            Loggers.error('❌ Failed to create controller for room: $roomID, error: $createError');
          }
        }
      } else {
        Loggers.error('❌ Cannot activate stream - roomID is null for index: $index');
      }
    } else {
      Loggers.error('❌ Invalid index: $index, livestreams length: ${widget.livestreams.length}');
    }
  }

  void _deactivateCurrentStream() {
    if (_currentActiveRoomID != null) {
      Loggers.info('Deactivating stream for room: $_currentActiveRoomID');

      try {
        // Find and properly dispose the controller
        final controller = Get.find<LivestreamScreenController>(tag: _currentActiveRoomID);

        // Perform immediate cleanup for faster switching
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $_currentActiveRoomID: $error');
        });

        // Delete the controller immediately - don't wait for logout
        Get.delete<LivestreamScreenController>(tag: _currentActiveRoomID);
        Loggers.info('Controller disposed for room: $_currentActiveRoomID');
      } catch (e) {
        Loggers.warning('Controller not found for room: $_currentActiveRoomID, error: $e');
      }

      _currentActiveRoomID = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        final isActive = index == _currentIndex;
        final roomID = widget.livestreams[index].roomID;

        Loggers.info('Building LiveStreamAudienceScreen - Index: $index, Active: $isActive, RoomID: $roomID, CurrentIndex: $_currentIndex');

        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
          isActive: isActive,
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/live_stream_audience_screen.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/room_manager.dart';
import 'package:ratulive/common/manager/logger.dart';

class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;

  const LiveStreamViewerScreen({
    super.key,
    required this.livestreams,
    required this.initialIndex,
  });

  @override
  State<LiveStreamViewerScreen> createState() => _LiveStreamViewerScreenState();
}

class _LiveStreamViewerScreenState extends State<LiveStreamViewerScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  String? _currentActiveRoomID;

  // Track all created controllers for better management
  final Set<String> _createdControllers = <String>{};

  // Use global room manager
  final RoomManager _roomManager = RoomManager();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Initialize the first stream controller immediately
    _activateStreamAtIndex(_currentIndex);

    // Pre-create controllers for adjacent pages for smoother transitions
    _preCreateAdjacentControllers();
  }

  @override
  void dispose() {
    // Clean up all created controllers
    _cleanupAllControllers();
    _pageController.dispose();
    super.dispose();
  }

  void _cleanupAllControllers() {
    Loggers.info('🧹 Cleaning up all controllers: ${_createdControllers.length} controllers');

    for (final roomID in _createdControllers.toList()) {
      try {
        final controller = Get.find<LivestreamScreenController>(tag: roomID);
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $roomID: $error');
        });
        Get.delete<LivestreamScreenController>(tag: roomID);
        Loggers.info('✅ Controller cleaned up for room: $roomID');
      } catch (e) {
        Loggers.warning('⚠️ Controller not found during cleanup for room: $roomID');
      }
    }

    _createdControllers.clear();
    _currentActiveRoomID = null;
  }

  void _onPageChanged(int index) {
    Loggers.info('📱 Page changed event - Old index: $_currentIndex, New index: $index');

    if (_currentIndex != index) {
      Loggers.info('🔄 Switching from stream $_currentIndex to stream $index');

      // Store old index for deactivation
      final oldIndex = _currentIndex;

      // Update current index FIRST so isActive calculations are correct
      _currentIndex = index;

      // Activate the new stream
      Loggers.info('🚀 Activating new stream at index $index...');
      _activateStreamAtIndex(index);

      // Deactivate the previous stream
      Loggers.info('🛑 Deactivating previous stream at index $oldIndex...');
      _deactivateStreamAtIndex(oldIndex);

      // Force rebuild to update isActive states
      if (mounted) {
        setState(() {});
      }

      Loggers.info('✅ Stream switching completed - Old: $oldIndex, New: $index');
    } else {
      Loggers.info('ℹ️ Page changed to same index, no action needed');
    }
  }

  void _preCreateAdjacentControllers() {
    // Pre-create controllers for previous and next streams for smoother transitions
    final prevIndex = _currentIndex - 1;
    final nextIndex = _currentIndex + 1;

    if (prevIndex >= 0) {
      _createControllerForIndex(prevIndex, isActive: false);
    }

    if (nextIndex < widget.livestreams.length) {
      _createControllerForIndex(nextIndex, isActive: false);
    }
  }

  void _createControllerForIndex(int index, {bool isActive = true}) {
    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      if (roomID != null) {
        // Skip if we already know this controller exists
        if (_createdControllers.contains(roomID)) {
          try {
            Get.find<LivestreamScreenController>(tag: roomID);
            Loggers.info('✅ Controller already exists for room: $roomID (index: $index)');
            return;
          } catch (e) {
            // Controller was supposed to exist but doesn't - remove from tracking
            _createdControllers.remove(roomID);
            _roomManager.cleanupRoom(roomID);
            Loggers.warning('⚠️ Controller tracking inconsistency for room: $roomID, removing from tracking');
          }
        }

        // Check if room is currently being joined by another controller
        if (_roomManager.isRoomBeingJoined(roomID)) {
          Loggers.warning('⚠️ Room $roomID is already being joined, skipping duplicate creation');
          return;
        }

        // Check if room has an active controller
        if (_roomManager.hasActiveController(roomID)) {
          Loggers.info('✅ Room $roomID already has active controller');
          _createdControllers.add(roomID);
          return;
        }

        // Check if room has previous errors
        final roomError = _roomManager.getRoomError(roomID);
        if (roomError != null) {
          Loggers.warning('⚠️ Room $roomID has previous error: $roomError');
          // Don't return here - allow retry
        }

        try {
          // Check if controller already exists
          Get.find<LivestreamScreenController>(tag: roomID);
          _createdControllers.add(roomID);
          Loggers.info('✅ Controller already exists for room: $roomID (index: $index)');
        } catch (e) {
          // Controller doesn't exist, create it
          try {
            // Mark room as being joined in room manager
            _roomManager.markRoomAsBeingJoined(roomID);

            Loggers.info('🔨 Creating controller for room: $roomID (preventing duplicates)');
            final newController = Get.put(
              LivestreamScreenController(livestream.obs, false),
              tag: roomID,
            );
            _createdControllers.add(roomID);

            // Mark room as successfully joined
            _roomManager.markRoomAsJoined(roomID, roomID);

            Loggers.success('✅ Controller created for room: $roomID (index: $index, active: $isActive)');

            // Verify the controller can be found immediately after creation
            try {
              Get.find<LivestreamScreenController>(tag: roomID);
              Loggers.success('✅ Controller verification successful for room: $roomID');
            } catch (verifyError) {
              Loggers.error('❌ Controller verification failed for room: $roomID, error: $verifyError');
              _createdControllers.remove(roomID);
              _roomManager.markRoomAsFailed(roomID, 'Controller verification failed');
            }
          } catch (createError) {
            Loggers.error('❌ Failed to create controller for room: $roomID, error: $createError');
            _createdControllers.remove(roomID);
            _roomManager.markRoomAsFailed(roomID, 'Controller creation failed: $createError');
          }
        }
      }
    }
  }

  void _activateStreamAtIndex(int index) {
    Loggers.info('🔄 _activateStreamAtIndex called with index: $index');

    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      Loggers.info('📋 Stream details - Index: $index, RoomID: $roomID, Host: ${livestream.hostUser?.username}');

      if (roomID != null) {
        Loggers.info('🚀 Activating stream for room: $roomID');
        _currentActiveRoomID = roomID;

        // Ensure controller exists for this stream
        _createControllerForIndex(index, isActive: true);

        // Pre-create controllers for adjacent streams
        _preCreateAdjacentControllers();
      } else {
        Loggers.error('❌ Cannot activate stream - roomID is null for index: $index');
      }
    } else {
      Loggers.error('❌ Invalid index: $index, livestreams length: ${widget.livestreams.length}');
    }
  }

  void _deactivateStreamAtIndex(int index) {
    if (index >= 0 && index < widget.livestreams.length) {
      final livestream = widget.livestreams[index];
      final roomID = livestream.roomID;

      if (roomID != null && _createdControllers.contains(roomID)) {
        Loggers.info('🛑 Deactivating stream at index $index for room: $roomID');
        try {
          // Find and properly dispose the controller
          final controller = Get.find<LivestreamScreenController>(tag: roomID);

          // Perform immediate cleanup for faster switching
          controller.logoutRoom(5).catchError((error) {
            Loggers.error('Error during logout for room $roomID: $error');
          });

          // Delete the controller immediately - don't wait for logout
          Get.delete<LivestreamScreenController>(tag: roomID);
          _createdControllers.remove(roomID);
          _roomManager.cleanupRoom(roomID);
          Loggers.success('✅ Controller disposed for room: $roomID');
        } catch (e) {
          Loggers.warning('⚠️ Controller not found for room: $roomID, error: $e');
          _createdControllers.remove(roomID);
          _roomManager.cleanupRoom(roomID);
        }
      }
    }
  }

  void _deactivateCurrentStream() {
    if (_currentActiveRoomID != null) {
      Loggers.info('🛑 Deactivating current stream for room: $_currentActiveRoomID');

      try {
        // Find and properly dispose the controller
        final controller = Get.find<LivestreamScreenController>(tag: _currentActiveRoomID);

        // Perform immediate cleanup for faster switching
        controller.logoutRoom(5).catchError((error) {
          Loggers.error('Error during logout for room $_currentActiveRoomID: $error');
        });

        // Delete the controller immediately - don't wait for logout
        Get.delete<LivestreamScreenController>(tag: _currentActiveRoomID);
        _createdControllers.remove(_currentActiveRoomID);
        Loggers.success('✅ Controller disposed for room: $_currentActiveRoomID');
      } catch (e) {
        Loggers.warning('⚠️ Controller not found for room: $_currentActiveRoomID, error: $e');
        _createdControllers.remove(_currentActiveRoomID);
      }

      _currentActiveRoomID = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        final isActive = index == _currentIndex;
        final roomID = widget.livestreams[index].roomID;

        Loggers.info('📱 Building LiveStreamAudienceScreen - Index: $index, Active: $isActive, RoomID: $roomID, CurrentIndex: $_currentIndex');

        // CRITICAL: Ensure controller exists before building the widget
        if (isActive && roomID != null) {
          _createControllerForIndex(index, isActive: true);
        }

        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
          isActive: isActive,
        );
      },
    );
  }
}

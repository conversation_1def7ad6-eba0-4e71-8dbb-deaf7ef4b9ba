import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/languages/languages_keys.dart';
import 'package:ratulive/screen/coin_wallet_screen/coin_wallet_screen_controller.dart';
import 'package:ratulive/screen/coin_wallet_screen/widget/coin_wallet_list.dart';
import 'package:ratulive/screen/coin_wallet_screen/widget/coin_wallet_top_view.dart';
import 'package:ratulive/utilities/text_style_custom.dart';
import 'package:ratulive/utilities/theme_res.dart';

class CoinWalletScreen extends StatelessWidget {
  const CoinWalletScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CoinWalletScreenController());
    return Scaffold(
      body: Column(
        children: [
          const CoinWalletTopView(),
          const SizedBox(height: 15),
          Text(LKey.coinShop.tr,
              style: TextStyleCustom.unboundedRegular400(
                color: text<PERSON><PERSON><PERSON><PERSON>(context),
                fontSize: 17,
              )),
          const Sized<PERSON>ox(height: 5),
          Text(LKey.rechargeWallet.tr,
              style: TextStyleCustom.outFitLight300(color: textLight<PERSON>rey(context), fontSize: 17),
              textAlign: TextAlign.center),
          CoinWalletList(controller: controller)
        ],
      ),
    );
  }
}

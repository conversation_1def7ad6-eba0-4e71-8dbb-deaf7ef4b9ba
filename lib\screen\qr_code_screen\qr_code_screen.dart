import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:ratulive/common/extensions/string_extension.dart';
import 'package:ratulive/common/widget/custom_app_bar.dart';
import 'package:ratulive/common/widget/custom_image.dart';
import 'package:ratulive/common/widget/full_name_with_blue_tick.dart';
import 'package:ratulive/common/widget/gradient_border.dart';
import 'package:ratulive/languages/languages_keys.dart';
import 'package:ratulive/model/user_model/user_model.dart';
import 'package:ratulive/screen/qr_code_screen/qr_code_screen_controller.dart';
import 'package:ratulive/screen/share_sheet_widget/share_sheet_widget.dart';
import 'package:ratulive/utilities/asset_res.dart';
import 'package:ratulive/utilities/style_res.dart';
import 'package:ratulive/utilities/text_style_custom.dart';
import 'package:ratulive/utilities/theme_res.dart';

class QrCodeScreen extends StatelessWidget {
  const QrCodeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(QrCodeScreenController());
    return Scaffold(
      body: Column(
        children: [
          CustomAppBar(title: LKey.myQrCode.tr),
          const SizedBox(height: 20),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    RepaintBoundary(
                      key: controller.screenshotKey,
                      child: Container(
                        color: whitePure(context),
                        padding: const EdgeInsets.symmetric(
                            vertical: 20, horizontal: 20),
                        child: Column(
                          spacing: 30,
                          children: [
                            Text(LKey.scanCodeToCheckProfile.tr,
                                style: TextStyleCustom.unboundedRegular400(
                                    color: textDarkGrey(context), fontSize: 15),
                                textAlign: TextAlign.center),
                            Obx(
                              () => GradientBorder(
                                  strokeWidth: 15,
                                  radius: 50,
                                  gradient: StyleRes.themeGradient,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      PrettyQrView.data(
                                        data: controller.branchLink.value,
                                        decoration: const PrettyQrDecoration(
                                          quietZone:
                                              PrettyQrQuietZone.modules(3),
                                        ),
                                      ),
                                      CustomImage(
                                          size: const Size(40, 40),
                                          image: controller
                                              .myUser.value?.profilePhoto
                                              ?.addBaseURL(),
                                          fullName:
                                              controller.myUser.value?.fullname,
                                          strokeColor: whitePure(context),
                                          strokeWidth: 5)
                                    ],
                                  )),
                            ),
                            Obx(() {
                              User? user = controller.myUser.value;
                              return Column(
                                children: [
                                  FullNameWithBlueTick(
                                    username: user?.username,
                                    fontSize: 14,
                                    iconSize: 20,
                                    isVerify: user?.isVerify,
                                  ),
                                  const SizedBox(height: 2),
                                  Text(user?.fullname ?? '',
                                      style: TextStyleCustom.outFitRegular400(
                                          color: textLightGrey(context),
                                          fontSize: 16)),
                                  const SizedBox(height: 15),
                                  Text(user?.bio ?? '',
                                      style: TextStyleCustom.outFitLight300(
                                          color: textLightGrey(context),
                                          fontSize: 15),
                                      textAlign: TextAlign.center),
                                ],
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                    SafeArea(
                      top: false,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              CustomAssetWithBgButton(
                                  onTap: () =>
                                      controller.saveGalleryImage('save'),
                                  image: AssetRes.icDownload,
                                  boxSize: 58,
                                  iconSize: 30),
                              Text(
                                LKey.save.tr,
                                style: TextStyleCustom.outFitLight300(
                                    color: textLightGrey(context),
                                    fontSize: 15),
                              )
                            ],
                          ),
                          const SizedBox(width: 54),
                          Column(
                            children: [
                              CustomAssetWithBgButton(
                                  onTap: () =>
                                      controller.saveGalleryImage('share'),
                                  image: AssetRes.icShare2,
                                  boxSize: 58,
                                  iconSize: 30),
                              Text(
                                LKey.share.tr,
                                style: TextStyleCustom.outFitLight300(
                                    color: textLightGrey(context),
                                    fontSize: 15),
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ]),
            ),
          ),
        ],
      ),
    );
  }
}

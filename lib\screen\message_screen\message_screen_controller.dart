import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/common/controller/base_controller.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/common/manager/session_manager.dart';
import 'package:ratulive/common/widget/confirmation_dialog.dart';
import 'package:ratulive/languages/languages_keys.dart';
import 'package:ratulive/model/chat/chat_thread.dart';
import 'package:ratulive/model/user_model/user_model.dart';
import 'package:ratulive/utilities/firebase_const.dart';

class MessageScreenController extends BaseController {
  List<String> chatCategories = [LKey.chats.tr, LKey.requests.tr];
  RxInt selectedChatCategory = 0.obs;
  FirebaseFirestore db = FirebaseFirestore.instance;
  PageController pageController = PageController();
  User? myUser = SessionManager.instance.getUser();
  RxList<ChatThread> chatsUsers = <ChatThread>[].obs;
  RxList<ChatThread> requestsUsers = <ChatThread>[].obs;

  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: selectedChatCategory.value);
    _fetchUsers();
  }

  void onPageChanged(int index) {
    selectedChatCategory.value = index;
  }

  void _fetchUsers() {
    db
        .collection(FirebaseConst.users)
        .doc(myUser?.id.toString())
        .collection(FirebaseConst.usersList)
        .withConverter(
          fromFirestore: (snapshot, options) => ChatThread.fromJson(snapshot.data()!),
          toFirestore: (ChatThread value, options) => value.toJson(),
        )
        .where(FirebaseConst.isDeleted, isEqualTo: false)
        .orderBy(FirebaseConst.id, descending: true)
        .snapshots()
        .listen((event) {
      for (var change in event.docChanges) {
        final ChatThread? updatedUser = change.doc.data();
        if (updatedUser == null) continue;

        switch (change.type) {
          case DocumentChangeType.added:
            if (updatedUser.chatType == ChatType.approved) {
              chatsUsers.add(updatedUser);
            } else {
              requestsUsers.add(updatedUser);
            }
            break;
          case DocumentChangeType.modified:
            // Remove the user from their current list
            final userId = change.doc.data()?.chatUser?.userId;
            chatsUsers.removeWhere((user) => user.chatUser?.userId == userId);
            requestsUsers.removeWhere((user) => user.chatUser?.userId == userId);

            // Get the updated user data
            final updatedUser = change.doc.data();

            if (updatedUser != null) {
              // Add the updated user to the correct list
              (updatedUser.chatType == ChatType.approved
                      ? chatsUsers
                      : requestsUsers)
                  .add(updatedUser);
            }
          case DocumentChangeType.removed:
            // Remove the user from their current list
            final userId = change.doc.data()?.chatUser?.userId;
            chatsUsers.removeWhere((user) => user.chatUser?.userId == userId);
            requestsUsers.removeWhere((user) => user.chatUser?.userId == userId);
            break;
        }
      }

      chatsUsers.sort(
        (a, b) {
          return (b.id ?? '0').compareTo(a.id ?? '0');
        },
      );
      requestsUsers.sort(
        (a, b) {
          return (b.id ?? '0').compareTo(a.id ?? '0');
        },
      );

      // Loggers.success('CHAT USER: ${chatsUsers.length}');
      // Loggers.success('REQUEST USER: ${requestsUsers.length}');
    });
  }

  void onLongPress(ChatThread chatConversation) {
    Get.bottomSheet(ConfirmationSheet(
      title: LKey.deleteChatUserTitle
          .trParams({'user_name': chatConversation.chatUser?.username ?? ''}),
      description: LKey.deleteChatUserDescription.tr,
      onTap: () async {
        int time = DateTime.now().millisecondsSinceEpoch;
        showLoader();
        await db
            .collection(FirebaseConst.users)
            .doc(myUser?.id.toString())
            .collection(FirebaseConst.usersList)
            .doc(chatConversation.chatUser?.userId.toString())
            .update({
          FirebaseConst.deletedId: time,
          FirebaseConst.isDeleted: true,
        }).catchError((error) {
          Loggers.error('USER NOT DELETE : $error');
        });
        stopLoader();
      },
    ));
  }
}

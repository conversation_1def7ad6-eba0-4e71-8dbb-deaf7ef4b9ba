class LKey {
  static const String language = "Language";
  static const String fromAroundTheWorld = "FROM AROUND\nTHE WORLD";
  static const String continueText = "Continue";
  static const String shareYourTalent =
      "Share your talent with the\npeople around the world\nand build your healthy fanbase.";
  static const String next = "Next";
  static const String getAppreciated = "GET\nAPPRECIATED";
  static const String select = "Select";
  static const String collectLikesAndComments =
      "Collect likes and comments\nfrom your fans, which boosts\nyou to create more content!";
  static const String multipleSocialGateways = "MULTIPLE SOCIAL\nGATEWAYS";
  static const String postContentOptions =
      "Post Reels, Images, Videos, Go\nLive. Choose any way\nfits to you & your audience!";
  static const String nearby = "Nearby";
  static const String following = "Following";
  static const String followersOnly = "Followers Only";
  static const String everyone = "Everyone";
  static const String follow = "Follow";
  static const String more = "More";
  static const String less = "Less";
  static const String comments = "Comments";
  static const String reply = "Reply";
  static const String likes = "Likes";
  static const String view = "View";
  static const String hide = "Hide";
  static const String replies = "Replies";
  static const String views = "Views";
  static const String post = "POST";
  static const String posts = "Posts";
  static const String writeHere = "Write here";
  static const String whatDoYouThink = "What do you think";
  static const String replyingTo = "Replying to";
  static const String coinsYouHave = "Coins You Have";
  static const String coins = "Coins";
  static const String coin = "Coin";
  static const String send = "Send";
  static const String sendGifts = "Send Gifts";
  static const String yourGiftHasBeenSent = "Your gift has been sent";
  static const String successfully = "Successfully";
  static const String checkVideos = "Check Videos";
  static const String audioDetails = "Audio Details";
  static const String makeReel = "Make Reel";
  static const String reels = "Reels";
  static const String reportPost = "Report @reportType";
  static const String report = "Report";
  static const String country = "Country";
  static const String description = "Description";
  static const String submit = "Submit";
  static const String agreeToPolicy =
      "By proceeding forward, You agree to the,";
  static const String privacyPolicy = "Privacy Policy";
  static const String andText = "and";
  static const String termsOfUse = "Terms of Use";
  static const String descriptionHere = "Description here..";
  static const String signIn = "SIGN IN";
  static const String toContinue = "TO CONTINUE";
  static const String enterYourEmail = "Enter Your Email";
  static const String enterPassword = "Enter Password";
  static const String forgetPassword = "Forget Password?";
  static const String logIn = "Log in";
  static const String logOut = "Log out";
  static const String deleteAccount = "Delete Account";
  static const String createAccountHere = "Create account here!";
  static const String continueWith = "Continue With";
  static const String signUp = "SIGN UP &";
  static const String startJourney = "START JOURNEY";
  static const String fullName = "Full Name";
  static const String email = "Email";
  static const String password = "Password";
  static const String reTypePassword = "Re-Type Password";
  static const String enterHere = "Enter here..";
  static const String createAccount = "Create Account";
  static const String enterFullName = "Enter Full Name";
  static const String livestreams = "LIVESTREAMS";
  static const String searchHere = "Search here..";
  static const String viewers = "Viewers";
  static const String messages = "Messages";
  static const String message = "Message";
  static const String chats = "Chats";
  static const String requests = "Requests";
  static const String followers = "Followers";
  static const String lvl = "LVL";
  static const String settings = "Settings";
  static const String openSettings = "Open Setting";
  static const String publish = "Publish";
  static const String website = "Website";
  static const String instagram = "Instagram";
  static const String youtube = "Youtube";
  static const String shopNow = "Shop Now";
  static const String feed = "Feed";
  static const String activity = "Activity";
  static const String system = "System";
  static const String story = "Story";
  static const String unFollow = "Unfollow";
  static const String goLive = "Go Live";
  static const String levels = "Levels";
  static const String gatherMoreCoins =
      "Gather more coins, ascend to new heights, and proudly showcase your badge on your profile for all to admire.";
  static const String level = "Level";
  static const String collection = "Collection";
  static const String you = "You";
  static const String become = "Become";
  static const String plus = "PLUS+";
  static const String personal = "Personal";
  static const String editProfile = "Edit Profile";
  static const String savedPosts = "Saved Posts";
  static const String languages = "Languages";
  static const String blockedUsers = "Blocked Users";
  static const String myQrCode = "My QR Code";
  static const String coinWallet = "Coin Wallet";
  static const String privacy = "Privacy";
  static const String whoCanSeePosts = "Who Can See Posts";
  static const String showMyFollowings = "Show My Followings";
  static const String showChatBtn = "Show Chat Button";
  static const String notifications = "Notifications";
  static const String general = "General";
  static const String postLikes = "Post Likes";
  static const String commentsOnPost = "Comments On Post";
  static const String mentions = "Mentions";
  static const String giftsReceived = "Gifts Received";
  static const String chatMessage = "Chat Message";
  static const String subscribeToPlus =
      "Subscribe to Plus and have \nad-free experience and verified\nicon on your profile.";
  static const String noAds = "No Ads";
  static const String getVerified = "Get Verified";
  static const String subscribeNow = "Subscribe Now";
  static const String subscriptionTerms =
      "The subscription renews automatically as per the plan unless cancelled within 24 hours before the end of the current period. You can manage and cancel subscriptions anytime from the app store/play store settings.";
  static const String userId = "User ID";
  static const String profileImage = "Profile Image";
  static const String username = "Username";
  static const String bio = "Bio";
  static const String phoneNumber = "Phone Number";
  static const String links = "Links";
  static const String link = "Link";
  static const String title = "Title";
  static const String addLink = "Add Link";
  static const String editLink = "Edit Link";
  static const String edit = "Edit";
  static const String delete = "Delete";
  static const String save = "Save";
  static const String share = "Share";
  static const String sharePost = "Share Post";
  static const String pin = "Pin";
  static const String unpin = "Unpin";
  static const String block = "Block";
  static const String unBlock = "UnBlock";
  static const String scanCodeToCheckProfile =
      "Scan This code\nto check the profile";
  static const String balance = "Balance";
  static const String collected = "Collected*";
  static const String gifted = "Gifted*";
  static const String gif = "GIF";
  static const String purchased = "Purchased*";
  static const String coinShop = "Coin Shop";
  static const String rechargeWallet =
      "Recharge your wallet. And\nsend gifts to your favorite creators.";
  static const String purchase = "Purchase";
  static const String withdrawals = "Withdrawals";
  static const String requestWithdrawal = "Request Withdrawal";
  static const String pending = "Pending";
  static const String completed = "Completed";
  static const String rejected = "Rejected";
  static const String coinBalance = "Coin Balance";
  static const String estimatedValue = "Estimated Value";
  static const String estimatedAmount = "Estimated Amount";
  static const String amount = "Amount";
  static const String enterCoinAmount = "Enter Coin Amount..";
  static const String convertedAmount = "Converted Amount";
  static const String createFeed = "Create Feed";
  static const String writeSomethingHere = "Write something here..";
  static const String mention = "Mention";
  static const String hashtags = "Hashtags";
  static const String location = "Location";
  static const String allowComments = "Allow Comments";
  static const String selectMusic = "Select Music";
  static const String explore = "Explore";
  static const String categories = "Categories";
  static const String saved = "Saved";
  static const String preview = "Preview";
  static const String changeCover = "Change Cover";
  static const String uploadNow = "Upload Now";
  static const String discard = "Discard";
  static const String done = "Done";
  static const String fontFamily = "Font Family";
  static const String typeSomething = "Type Something...";
  static const String users = "Users";
  static const String user = "User";
  static const String places = "Places";
  static const String enterLiveStreamTitle = "Enter Live Stream Title...";
  static const String startLive = "Start Live";
  static const String stop = "Stop";
  static const String startBattle = "Start Battle";
  static const String battleStartingIn = "Battle\nStarting\nin";
  static const String cancel = "Cancel";
  static const String yes = "Yes";
  static const String members = "Members";
  static const String audience = "Audience";
  static const String invited = "Invited";
  static const String invite = "Invite";
  static const String coHosts = "Co-hosts";
  static const String refuse = "Refuse";
  static const String accept = "Accept";
  static const String host = "Host";
  static const String guest = "Guest";
  static const String victory = "Victory";
  static const String defeat = "Defeat";
  static const String streamedFor = "Streamed For";
  static const String followersGained = "Followers Gained";
  static const String totalCoinsCollected = "Total Coins Collected";
  static const String goHome = "Go Home";
  static const String streamEnded = "Stream Ended!";
  static const String yourStreamEnded = "Your Stream Ended!";
  static const String belowIsTheSummaryOfYourStream =
      "Below is the summary of your stream!";
  static const String fromBattle = "From Battle";
  static const String live = "Live";
  static const String checkProfile = "Check Profile";
  static const String youAreSendingCoinsTo =
      "You are sending coins to @color Participant";
  static const String youWillBeSentBackToMainView =
      "You will be sent back to main view in...";
  static const String streamEndedMinUserDescription =
      "The live stream has ended because min. @viewers_count viewers are required to keep it going.";
  static const String enterEmail =
      "Please enter an email. The email field is empty.";
  static const String enterAPassword =
      "Please enter a password. The password field is empty.";
  static const String noUserFound =
      "No user found with that email. Please register with this email.";
  static const String incorrectPassword =
      "Incorrect password provided for this user.";
  static const String weakPassword = "The password provided is too weak.";
  static const String accountExists =
      "The account already exists for that email.";
  static const String fullNameEmpty =
      "Please enter a full name. The full name field is empty.";
  static const String usernameEmpty =
      "Please enter a username. The username field is empty.";
  static const String urlEmpty =
      "Please enter a URL link. The link field is empty.";
  static const String validUrl = "Please enter a valid URL link";
  static const String urlTitleEmpty =
      "Please enter a title. The title field is empty.";
  static const String validUsernameEmpty = "Please enter a valid username.";
  static const String confirmPasswordEmpty =
      "Please confirm your password. The confirm password field is empty.";
  static const String passwordMismatch =
      "The password and confirm password do not match.";
  static const String invalidEmail =
      "The email is incorrect. Please enter a valid email.";
  static const String verificationLinkSent =
      "A verification link has been sent to your email. Please verify the link to log in.";
  static const String verifyEmailFirst =
      "Please verify your email first, then proceed to log in.";
  static const String deleteLinkTitle = "Delete Link?";
  static const String phoneCode = "Phone Code";
  static const String noData = "No Data Found";
  static const String deleteLinkDescription =
      "Are you sure you want to delete this link? This action cannot be undone.";
  static const String postIsBeginUploading = "Uploading post...";
  static const String storyIsBeginUploading = "Uploading story...";
  static const String postUploadSuccessfully = "Post uploaded successfully.";
  static const String storyUploadSuccess = "Story uploaded successfully.";
  static const String uploadingFailed = "Uploading failed.";
  static const String addFilter = "Add Filter";
  static const String applyAll = "Apply to all";
  static const String anErrorOccurredWhileApplyingTheVideoFilter =
      "An error occurred while applying the video filter.";
  static const String anErrorOccurredWhileCompressVideo =
      "An error occurred while compress the video.";
  static const String deletePostTitle = "Delete Post?";
  static const String deletePostMessage =
      "Are you sure you want to delete this post? Once deleted, it cannot be restored.";
  static const String postPinned = "Your post has been pinned.";
  static const String postUnpinned = "Your post has been unpinned.";
  static const String pinLimitExceeded = "You can pin maximum @pin_count post.";
  static const String pinLimitExceededForComment =
      "You can pin maximum @pin_comment_count comment.";
  static const String resetPasswordLinkSent =
      "A reset password link has been sent to your email. Please use it to reset your password.";
  static const String copyrightInfringement = "Copyright Infringement";
  static const String hateSpeech = "Hate Speech";
  static const String harassmentOrBullying = "Harassment or Bullying";
  static const String violenceOrThreats = "Violence or Threats";
  static const String nudityOrSexualContent = "Nudity or Sexual Content";
  static const String terrorismOrExtremistContent =
      "Terrorism or Extremist Content";
  static const String scamsOrFraud = "Scams or Fraud";
  static const String spam = "Spam";
  static const String impersonation = "Impersonation";
  static const String misinformation = "Misinformation";
  static const String reason = "Reason";
  static const String provideReportReason =
      "Please provide a detailed reason for the report.";
  static const String reportSubmitted = "Report submitted successfully.";
  static const String blockUser = "Block @username!";
  static const String unblockUser = "UnBlock @username!";
  static const String blockUserConfirmation =
      "Are you sure you want to block this user? You will no longer see their followers or following, and they won't be able to view your profile, photos, or posts. Once blocked, this user will no longer be able to chat with you, and you won’t be able to chat with them either.";
  static const String unblockUserConfirmation =
      "Once unblocked, this user will be able to chat with you again, and you can chat with them as well. They will also be able to see your profile, photos, and posts, and you will be able to view their followers and following.";
  static const String youAreBlockThisUser = "You are bock this user";
  static const String thisUserIsFreeze = "This user is freeze";
  static const String deleteCommentTitle = "Delete Comment!";
  static const String deleteReplyCommentTitle = "Delete Reply Comment!";
  static const String deleteCommentMessage =
      "Are you sure you want to delete this comment? This action cannot be undone.";
  static const String deleteAccountMessage =
      "Do you really want to delete your account? All of your data will be deleted, and you won’t be able to recover it again!";
  static const String proceedConfirmation = "Do you really want to proceed?";
  static const String logoutConfirmation =
      "Are you sure you want to log out? You will need to log in again to access your account.";
  static const String logoutTitle = "Logout!";
  static const String deleteYourAccount = "Delete Your Account!";
  static const String noContentMessage =
      "There is nothing we can show you for now.";
  static const String music = "music";
  static const String discardEditsTitle = "Discard edits?";
  static const String startAgainTitle = "Start Again?";
  static const String startAgain = "Start Again";
  static const String discardEditsMessage =
      "If you go back now, you'll lose all the edits you've made.";
  static const String startAgainMessage =
      "If you go back now, you'll lose this draft";
  static const String discover = "Discover";
  static const String noLocationFound = "No location found.";
  static const String startSearchingLocation =
      "Start searching for a location.";
  static const String only = "Only";
  static const String currentValue = "Current Value";
  static const String accountDetails = "Account Details";
  static const String selectGateway = "Select Gateway";
  static const String youCanNotEnterMoreThanEtc =
      "You cannot enter more than @coin coins!";
  static const String redeemGatewayNotFound = "Redeem gateway not found.";
  static const String recordUpToSeconds = "Record up to @second seconds.";
  static const String cameraMicrophonePermissionTitle =
      "Allow Camera & Microphone Access";
  static const String cameraMicrophonePermissionDescription =
      "To capture photos, record videos, and use voice features, @app_name needs access to your camera and microphone. Please grant permission in your device settings.";
  static const String openSetting = "Open setting";
  static const String deleteStoryTitle = "Delete Story?";
  static const String deleteStoryMessage =
      "Are you sure you want to delete this story?";
  static const String deleteTextConfirmation =
      "Are you sure you want to delete this text?";
  static const String musicDownloadFailed = "Music download failed.";
  static const String viewMore = "View More";
  static const String downloadCompletedSuccessfully =
      "Download completed successfully!";
  static const String downloadHasStarted = "Download has started...";
  static const String userNotFound = "User not found.";
  static const String deleteMessageTitle = "Delete Message?";
  static const String deleteChatUserTitle = "Delete @user_name?";
  static const String deleteChatUserDescription =
      "After deleting this user, you will not be able to restore the chat.";
  static const String deleteMessageDescription =
      "This message will be deleted only for you. The other person will still be able to see it. Do you want to continue?";
  static const String sendMedia = "Send Media";
  static const String selectMedia = "Select Media";
  static const String video = "Video";
  static const String image = "Image";
  static const String youReceivedAGift = "You received a gift!";
  static const String youSentAGift = "You have sent a gift!";
  static const String enableMicrophoneAccessTitle = "Enable Microphone Access";
  static const String enableCameraAndMicrophoneAccessTitle =
      "Enable Camera and Microphone Access";
  static const String enablePhotoAccessTitle =
      "Enable photos and videos access";
  static const String enablePhotoAccessDescription =
      "To Share photos and videos, @app_name needs access to Storage on your device.";
  static const String enableCameraAndMicrophoneAccessDescription =
      "You'll need to enable camera and microphone access in your settings to send a photo or video message.";
  static const String enableMicrophoneAccessDescription =
      "You'll need to enable microphone access in your settings to send a voice message.";
  static const String settingsButton = "Settings";
  static const String deleteForYou = "Delete for you";
  static const String unSend = "Unsend";
  static const String shareLimitMessage =
      "You can share with up to @share_chat_limit chats.";
  static const String chatRequestMessage =
      "@chat_user_name wants to chat with you. Approve it if you want to chat.";
  static const String cannotMessageBlockedUser =
      "You cannot send messages to this account unless you unblock them.";
  static const String youBlockedUser =
      "You Blocked @block_user_name \nYou cannot send messages to this account unless you unblock them.";
  static const String youAreBlockedByThisUser = "You are blocked by this user.";
  static const String copiedToClipboard = "Copied to clipboard.";
  static const String messageSent = "Message sent";
  static const String storyUnavailable = "Story unavailable";
  static const String deleteMusicTitle = "Delete Music?";
  static const String deleteMusicMessage =
      "Are you sure you want to delete this music? This action cannot be undone.";
  static const String sessionExpiredTitle =
      "Session Expired – Logged in on Another Device";
  static const String sessionExpiredMessage =
      "For security reasons, @app_name allows only one active session per account. It looks like you logged in on another device using the same email. To protect your account, we have automatically logged you out from this device.\n\nIf this wasn’t you, please log in again. You can change your password from the login screen if needed.";
  static const String freezeTitle = "Your Account Has Been Frozen";
  static const String freezeAccountSubject = "Request to Unfreeze My Account";
  static const String freezeDescription =
      "We're sorry, but it looks like your account has been temporarily frozen. This might be due to unusual activity or a policy-related concern.\n\nFor your security and to ensure proper access, your account has been placed on hold. You won’t be able to use the app until the issue is resolved.\n\nTo unfreeze your account or to get more information, please reach out to our support team at @support_mail. We’re here to assist you and will do our best to help you get back up and running as quickly as possible.\n\nThank you for your patience and understanding.";
  static const String textRejectedAndContainsSuchThings =
      "Text is rejected because it includes such things: ";
  static const String mediaRejectedAndContainsSuchThings =
      "Media is rejected because it contains such things: ";
  static const String restrictUserRequests = "Restrict users requests to join.";
  static const String endStreamTitle = "End This Stream!";
  static const String endStreamMessage =
      "Do you really want to end this streaming?";
  static const String stopBattleTitle = "End Battle?";
  static const String stopBattleDescription =
      "Are you sure you want to stop the battle? This action can’t be undone.";
  static const String requestTitle = "No Join Requests Yet";
  static const String requestDescription =
      "View and manage audience requests to join the live stream.";
  static const String audienceListEmptyTitle = "No Viewers Yet";
  static const String audienceListEmptyDescription =
      "Audience members will appear here when they join your live stream.";
  static const String invitedListEmptyTitle = "No One Invited";
  static const String invitedListEmptyDescription =
      "You haven’t invited anyone yet. Invited users will be listed here.";
  static const String coHostListEmptyTitle = "No Co-hosts Yet";
  static const String coHostListEmptyDescription =
      "Co-hosts will appear here once they accept your invitation.";
  static const String sendingCoinsMessage =
      "You are sending coins to the above user.";
  static const String noLivestreamsTitle = "No Live Streams Yet";
  static const String noLivestreamsDescription =
      "Live streams will appear here when someone goes live.";
  static const String noUserReelsTitle = "No Reels Yet";
  static const String noUserReelsDescription =
      "This user hasn’t posted any reels so far.";
  static const String noMyPostsTitle = "You Haven’t Shared Any Posts";
  static const String noMyPostsDescription =
      "Start posting to share your moments with the community.";
  static const String noUserPostsTitle = "No Posts Yet";
  static const String noUserPostsDescription =
      "This user hasn’t shared any posts so far.";
  static const String chatListEmptyTitle = "Start a Conversation";
  static const String chatListEmptyDescription =
      "Your chats will show up here once you start messaging someone.";
  static const String chatRequestEmptyTitle = "No Requests Right Now";
  static const String chatRequestEmptyDescription =
      "New chat requests will appear here when someone messages you.";
  static const String joinRequestSentDescription =
      "Your request to join the live stream is pending approval.";
  static const String joinedTheStream = "Joined the stream";
  static const String join = "Join";
  static const String wantsYouToBeEtc = "wants you to be in this live video";
  static const String requestingToJoinTheStream =
      "requesting to join the stream.";
  static const String requestDeclinedByHost =
      "The host has declined your request to join the live stream.";
  static const String requestJoinToHost =
      "Your request to join the live stream has been sent to the host.";
  static const String exitLiveStream = "Exit live stream?";
  static const String ifYouCheckThisProfileEtc =
      "If you check this profile, you will exit the live stream.";
  static const String cannotLeaveDuringBattle =
      "You can’t leave the live stream while a battle is in progress.";
  static const String getBack = "Get Back";
  static const String exitLiveStreamTitle = "Exit Live Stream?";
  static const String exitLiveStreamDescription =
      "Are you sure you want to leave the live stream?\nIf the stream has ended, you won't be able to join again. ";
  static const String scanQrCode = "Scan QR Code";
  static const String scanQrProfileSearch =
      "Scan the QR of the profile you want to search.";
  static const String uploadFromGallery = "Upload From Gallery";
  static const String reelsEmptyTitle = "Nothing to Watch Yet";
  static const String reelsEmptyDescription =
      "Reels will appear here once new content is shared.";
  static const String searchPageEmptyTitle = "Nothing to Show Yet";
  static const String searchPageEmptyDescription =
      "Explore content by entering keywords in the search bar above.";
  static const String km = "km";
  static const String qrCodeMessage =
      "an artist based in Portland. I find inspiration in nature and urban life. Let’s connect and create something beautiful! 🌿🖌️";
  static const String videoPathNotFound = "Video path not found.";
  static const String activityLikedPost = "has liked your post";
  static const String commentHasBeenLiked = "liked your comment.";
  static const String activityCommentedPost =
      "has commented on your post : @comment_description";
  static const String activityGIFComment =
      "has commented on your post with a GIF.";
  static const String activityReplyingToComment =
      "@username replied to your comment : @comment_description";
  static const String notifyMentionedInPost = "has mentioned you in post.";
  static const String notifyMentionedInComment = "Mentioned you in comment";
  static const String notifyStartedFollowing = "has started following you";
  static const String notifyReplyMentionedInComment =
      "has reply mentioned you in comment : @comment_description";
  static const String activitySentGift = "has sent you gift";
  static const String postCommentEmptyTitle = "No Comments Yet";
  static const String postCommentEmptyDescription =
      "This post doesn’t have any comments. Join in and share your thoughts.";
  static const String postSentSuccessfully = "Post sent successfully";
  static const String searchGiphy = "Search @brand_name";
  static const String nearbyReelsPermissionTitle = "Find Reels Around You";
  static const String nearbyReelsPermissionDescription =
      "Allow location access to explore reels shared by people near you.";
  static const String locationServicesDisabledTitle =
      "Location Services Are Still Disabled";
  static const String locationServicesDisabledDescription =
      "To see nearby reels, please turn on location services in your device settings.";
  static const String seePlacesNearYou = "See places near you.";
  static const String turnOnLocationServicesMessage =
      "To include nearby places, turn on location services.";
  static const String turnOnLocationServicesButton =
      "Turn on Location Services";
  static const String nothingToShowHere = "Nothing To Show Here";
  static const String userHidFollowings =
      "This user has decided not to show their followings.";
  static const String blockListEmptyTitle = "No Blocked Users";
  static const String blockListEmptyDescription =
      "Users you block will appear here. You haven’t blocked anyone yet.";
  static const String redeemMinCoinDescription =
      "You need to reach the minimum coin amount before you can redeem.";
  static const String userListEmptyTitle = "No Users Found";
  static const String userListEmptyDescription =
      "Users will appear here when available.";
  static const String freeze = "Freeze";
  static const String unFreeze = "UnFreeze";
  static const String profileTemporarilyFrozen =
      "This profile has been temporarily frozen due to policy violations or suspicious activity. Access is restricted until review is complete.";
  static const String profileUnavailable = "Profile Unavailable";
  static const String admin = "Admin";
  static const String days = "Days";
  static const String weeks = "Weeks";
  static const String months = "Months";
  static const String years = "Years";
  static const String day = "Day";
  static const String week = "Week";
  static const String month = "Month";
  static const String year = "Year";
  static const String lifetime = "Lifetime";
  static const String annual = "Annual";
  static const String sixMonth = "6 Month";
  static const String threeMonth = "3 Month";
  static const String twoMonth = "2 Month";
  static const String monthly = "Monthly";
  static const String weekly = "Weekly";
  static const String subscriptionDescription =
      "@price / Month. Billed @unit_label";
  static const String giveItATry = "Give it a try";
  static const String freeTrialDescription = "@count @get_period Free trial";
  static const String annually = "Annually";
  static const String semiAnnually = "Semi Annually";
  static const String threeMonths = "Three Months";
  static const String twoMonths = "Two Months";
  static const String joinCancelledDescription =
      "The host has cancelled your invitation. You can no longer join the live stream.";
  static const String joinedAsACoHost = "joined as a co-host";
  static const String minFollowersNeededToGoLive =
      "Min. @count Followers needed to go Live";
  static const String maxUserLinkAddDescription =
      "You can add only @limit links in the profile.";
  static const String refresh = "Refresh";
  static const String lost = "Lost";
  static const String connection = "Connection";
  static const String noInternetDesc =
      "It looks like you are not\nconnected to the internet";
  static const String downloadingFailed = "Download failed.";
  static const String from = "From";
  static const String somethingWentWrong = "Something went wrong.";
  static const String registrationBonusTitle =
      "🎉 Welcome Bonus Credited to Your Wallet!";
  static const String registrationBonusDescription =
      "You've earned your registration bonus! Enjoy your coins and start exploring now!";
  static const String youAre = "You are";
  static const String member = "member";
  static const String battleEndedGiftNotSent =
      "Oops!! Battle Ended Please wait for a while..";
  static const String liveStreamNotificationTitle = "Hey, @name is now live";
  static const String liveStreamNotificationBody =
      "Hurry up and join before it ends.";
  static const String livestreamHasEnded = "Livestream has ended.";
  static const String theHostHasTurnedOffYourVideo =
      "The host has turned off your Video.";
  static const String theHostHasTurnedOffYourAudio =
      "The host has turned off your Audio.";
  static const String theCoHostHasTurnedOffTheirVideo =
      "The Co-Host has turned off your Video.";
  static const String theCoHostHasTurnedOffTheirAudio =
      "The Co-Host has turned off your Audio.";
}

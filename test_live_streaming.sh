#!/bin/bash

echo "🚀 Starting Live Streaming Functionality Tests"
echo "=============================================="

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

echo "✅ Flutter found: $(flutter --version | head -n 1)"

# Clean and get dependencies
echo "📦 Getting dependencies..."
flutter clean
flutter pub get

# Generate mocks for testing
echo "🔧 Generating mocks..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run the live streaming tests
echo "🧪 Running live streaming tests..."
flutter test test/live_streaming_test.dart --coverage

# Check if tests passed
if [ $? -eq 0 ]; then
    echo "✅ All live streaming tests passed!"
else
    echo "❌ Some tests failed. Please check the output above."
    exit 1
fi

# Run static analysis
echo "🔍 Running static analysis..."
flutter analyze

# Check for any analysis issues
if [ $? -eq 0 ]; then
    echo "✅ Static analysis passed!"
else
    echo "⚠️  Static analysis found some issues. Please review."
fi

echo ""
echo "🎉 Live Streaming Test Summary:"
echo "================================"
echo "✅ Camera toggle error handling"
echo "✅ Camera rotation functionality"
echo "✅ Video enable/disable with error recovery"
echo "✅ Microphone toggle with error handling"
echo "✅ Stream publishing error handling"
echo "✅ Scroll-related authentication fixes"
echo "✅ Robust error handling for all operations"
echo ""
echo "🔧 Manual Testing Checklist:"
echo "=============================="
echo "1. Test camera toggle during live streaming"
echo "2. Test camera rotation (front/back) during streaming"
echo "3. Test turning camera on/off during streaming"
echo "4. Test microphone toggle during streaming"
echo "5. Test scrolling in live stream viewer without login errors"
echo "6. Test error scenarios (permissions denied, network issues)"
echo "7. Test from both streamer and viewer perspectives"
echo ""
echo "📝 Key Fixes Implemented:"
echo "========================="
echo "• Added try-catch blocks for all camera operations"
echo "• Implemented state reversion on operation failures"
echo "• Added validation checks before camera operations"
echo "• Improved error messages for better user experience"
echo "• Fixed scroll-related authentication redirects"
echo "• Added comprehensive logging for debugging"
echo "• Implemented graceful error recovery mechanisms"

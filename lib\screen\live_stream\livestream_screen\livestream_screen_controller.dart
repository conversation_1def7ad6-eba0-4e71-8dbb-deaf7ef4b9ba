import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ratulive/common/controller/ads_controller.dart';
import 'package:ratulive/common/controller/base_controller.dart';
import 'package:ratulive/common/controller/firebase_firestore_controller.dart';
import 'package:ratulive/common/extensions/user_extension.dart';
import 'package:ratulive/common/manager/firebase_notification_manager.dart';
import 'package:ratulive/common/manager/haptic_manager.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/common/manager/session_manager.dart';
import 'package:ratulive/common/service/api/notification_service.dart';
import 'package:ratulive/common/widget/confirmation_dialog.dart';
import 'package:ratulive/languages/languages_keys.dart';
import 'package:ratulive/model/general/settings_model.dart';
import 'package:ratulive/model/livestream/app_user.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/model/livestream/livestream_comment.dart';
import 'package:ratulive/model/livestream/livestream_user_state.dart';
import 'package:ratulive/model/user_model/user_model.dart';
import 'package:ratulive/screen/gift_sheet/send_gift_sheet.dart';
import 'package:ratulive/screen/gift_sheet/send_gift_sheet_controller.dart';
import 'package:ratulive/screen/live_stream/live_stream_end_screen/live_stream_end_screen.dart';
import 'package:ratulive/screen/live_stream/live_stream_end_screen/widget/livestream_summary.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/widget/live_stream_join_sheet.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/host/widget/live_stream_host_top_view.dart';
import 'package:ratulive/screen/report_sheet/report_sheet.dart';
import 'package:ratulive/utilities/app_res.dart';
import 'package:ratulive/utilities/asset_res.dart';
import 'package:ratulive/utilities/firebase_const.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:zego_express_engine/zego_express_engine.dart';

class LivestreamScreenController extends BaseController {
  FirebaseFirestore db = FirebaseFirestore.instance;
  ZegoExpressEngine zegoEngine = ZegoExpressEngine.instance;

  final firestoreController = Get.find<FirebaseFirestoreController>();
  final adsController = Get.find<AdsController>();

  Timer? timer;
  Timer? minViewerTimeoutTimer;
  Function? onLikeTap;

  Setting? get setting => SessionManager.instance.getSettings();

  int get minViewersThreshold => setting?.liveMinViewers ?? 0;

  int get timeoutMinutes => setting?.liveTimeout ?? 0;

  int get myUserId => SessionManager.instance.getUserID();

  RxBool isPlayerMute = false.obs;
  RxBool isMinViewerTimeout = false.obs;
  RxBool isTextEmpty = true.obs;
  bool isJoinSheetOpen = false;
  bool isFrontCamera = true;
  bool isHost;

  // Camera state tracking
  RxBool isCameraEnabled = true.obs;
  RxBool isCameraInitializing = false.obs;
  DateTime? lastCameraToggleTime;

  StreamSubscription<DocumentSnapshot<Livestream>>? liveStreamDocListener;
  StreamSubscription<QuerySnapshot<LivestreamUserState?>>?
      liveStreamUserStatesListener;
  StreamSubscription<QuerySnapshot<LivestreamComment?>>?
      liveStreamCommentsListener;

  TextEditingController textCommentController = TextEditingController();

  DocumentReference get liveStreamDocRef =>
      db.collection(FirebaseConst.liveStreams).doc(liveData.value.roomID);

  CollectionReference get liveStreamUsersRef =>
      db.collection(FirebaseConst.appUsers);

  CollectionReference get liveStreamUserStatesRef => db
      .collection(FirebaseConst.liveStreams)
      .doc(liveData.value.roomID)
      .collection(FirebaseConst.userState);

  CollectionReference get liveStreamCommentsRef => db
      .collection(FirebaseConst.liveStreams)
      .doc(liveData.value.roomID)
      .collection(FirebaseConst.comments);

  Widget? hostPreview;

  LivestreamScreenController(this.liveData, this.isHost, {this.hostPreview});

  int totalBattleSecond = 0;

  RxInt remainingBattleSeconds = 0.obs;
  RxBool isViewVisible = true.obs;

  List<LivestreamUserState> memberList = <LivestreamUserState>[];

  List<Gift> get gifts => setting?.gifts ?? [];
  RxList<LivestreamUserState> requestList = <LivestreamUserState>[].obs;
  RxList<LivestreamUserState> audienceList = <LivestreamUserState>[].obs;
  RxList<LivestreamUserState> invitedList = <LivestreamUserState>[].obs;
  RxList<LivestreamUserState> coHostList = <LivestreamUserState>[].obs;
  RxList<LivestreamUserState> audienceMemberList = <LivestreamUserState>[].obs;
  RxList<StreamView> streamViews = <StreamView>[].obs;
  RxList<LivestreamComment> comments = <LivestreamComment>[].obs;
  RxList<LivestreamUserState> liveUsersStates = <LivestreamUserState>[].obs;

  Rx<AppUser?> selectedGiftUser = Rx(null);
  Rx<VideoPlayerController?> videoPlayerController = Rx(null);

  Rx<User?> get myUser => SessionManager.instance.getUser().obs;
  Rx<Livestream> liveData;

  AudioPlayer countdownPlayer = AudioPlayer();
  AudioPlayer battleStartPlayer = AudioPlayer();
  AudioPlayer winAudioPlayer = AudioPlayer();

  @override
  void onInit() {
    super.onInit();
    if (liveData.value.isDummyLive == 1) {
      initVideoPlayer();
    } else {
      totalBattleSecond =
          Duration(minutes: liveData.value.battleDuration).inSeconds;
      remainingBattleSeconds.value = totalBattleSecond;
      zegoEngine.setAudioDeviceMode(ZegoAudioDeviceMode.General);

      // Add a small delay before attempting room login to ensure ZEGO engine is ready
      Future.delayed(const Duration(milliseconds: 300), () {
        if (!Get.isRegistered<LivestreamScreenController>(tag: liveData.value.roomID)) {
          Loggers.warning('⚠️ Controller was disposed before room login could complete');
          return;
        }
        loginRoom();
      });

      startListenEvent();
    }

    // Common listeners for all users
    listenLiveStreamData();
    listenUserState();
    fetchLiveStreamComments();
    initAudioPlayer();
    _initializeCameraState(); // Initialize camera state tracking
    WakelockPlus.enable();
    FirebaseNotificationManager.instance
        .unsubscribeToTopic(topic: myUserId.toString());
  }

  @override
  void onClose() {
    super.onClose();
    WakelockPlus.disable();
    timer?.cancel();
    minViewerTimeoutTimer?.cancel();
    videoPlayerController.value?.dispose();
    liveStreamUserStatesListener?.cancel();
    liveStreamCommentsListener?.cancel();
    liveStreamDocListener?.cancel();
    countdownPlayer.dispose();
    winAudioPlayer.dispose();
    stopListenEvent();
    logoutRoom(4);
    if (!isHost) {
      updateLiveStreamData(
          watchingCount: -1, coHostId: FieldValue.arrayRemove([myUserId]));
    }
  }

  Future<void> initVideoPlayer() async {
    final url = liveData.value.dummyUserLink ?? '';
    if (url.isEmpty) {
      Loggers.warning('No dummy user link provided for video player');
      return;
    }

    try {
      // Dispose old controller if exists to avoid memory leak
      await videoPlayerController.value?.dispose();

      final controller = VideoPlayerController.networkUrl(Uri.parse(url));
      isPlayerMute.value = false;

      await controller.initialize();
      controller
        ..setLooping(true)
        ..play();

      videoPlayerController.value = controller;
      videoPlayerController.value?.setLooping(true);
      updateLiveStreamData(watchingCount: 1);

      Loggers.info('Video player initialized successfully for URL: $url');
    } on PlatformException catch (e) {
      Loggers.error('Platform exception in video player: ${e.message}');
      showSnackBar('Failed to load video stream: ${e.message}');
    } catch (e) {
      Loggers.error('Error initializing video player: $e');
      showSnackBar('Failed to initialize video player. Please try again.');
    }
  }

  void initAudioPlayer() {
    countdownPlayer.setAsset(AssetRes.endCountdown);
    battleStartPlayer.setAsset(AssetRes.battleStart);
    winAudioPlayer.setAsset(AssetRes.winSound);
  }

  Future<void> logoutRoom(int index) async {
    final roomID = liveData.value.roomID ?? '';

    try {
      Loggers.info('Logging out from room: $roomID (reason: $index)');

      if (isHost) {
        deleteStreamOnFirebase();
      }

      // Stop all streaming activities first
      await stopPreview();
      await stopPublish();

      // Logout from the room
      if (roomID.isNotEmpty) {
        await zegoEngine.logoutRoom(roomID);
        Loggers.success('Successfully logged out from room: $roomID');
      }

    } catch (e) {
      Loggers.error('Error during room logout: $e');
      // Continue with logout even if there are errors
      try {
        await zegoEngine.logoutRoom();
      } catch (fallbackError) {
        Loggers.error('Fallback logout also failed: $fallbackError');
      }
    }
  }

  Future<ZegoRoomLoginResult> loginRoom({int retryCount = 0}) async {
    final roomID = liveData.value.roomID ?? '';
    final user = ZegoUser('$myUserId', myUser.value?.username ?? '');

    // Enhanced debugging for room login
    Loggers.info('=== ZEGO Room Login Debug ===');
    Loggers.info('LiveData: ${liveData.value.toJson()}');
    Loggers.info('Room ID: "$roomID" (length: ${roomID.length})');
    Loggers.info('User ID: "$myUserId"');
    Loggers.info('Username: "${myUser.value?.username ?? 'null'}"');
    Loggers.info('Host ID: ${liveData.value.hostId}');
    Loggers.info('Is Host: $isHost');
    Loggers.info('Retry Count: $retryCount');
    Loggers.info('============================');

    if (roomID.isEmpty) {
      Loggers.error('❌ Cannot login to room: roomID is empty');
      throw Exception('Room ID is required for login');
    }

    // Validate room ID format
    if (!_isValidRoomID(roomID)) {
      Loggers.error('❌ Invalid room ID format: "$roomID"');
      throw Exception('Invalid room ID format');
    }

    final roomConfig = ZegoRoomConfig.defaultConfig()
      ..isUserStatusNotify = true;

    try {
      Loggers.info('🔄 Attempting to login to room: "$roomID" (attempt ${retryCount + 1})');

      // Ensure we're logged out of any previous room first
      Loggers.info('🚪 Logging out of any previous room...');
      await zegoEngine.logoutRoom();

      // Small delay to ensure logout is complete
      await Future.delayed(const Duration(milliseconds: 200));

      // Additional validation: Check if this is an audience member trying to join a non-existent room
      if (!isHost) {
        Loggers.info('👥 Audience member joining room - checking room existence...');
        // For audience members, we need to ensure the host has created the room
        // We'll rely on Firebase to validate the stream exists
        final streamExists = await _validateStreamExists(roomID);
        if (!streamExists) {
          Loggers.error('❌ Stream does not exist in Firebase for room: $roomID');
          throw Exception('Stream no longer exists');
        }
      }

      Loggers.info('🔑 Starting ZEGO room login...');
      final result =
          await zegoEngine.loginRoom(roomID, user, config: roomConfig);

      if (result.errorCode != 0) {
        final errorMessage = _getLoginErrorMessage(result.errorCode);
        Loggers.error('❌ Room login failed: ${result.errorCode} - $errorMessage');
        Loggers.error('❌ Room ID: "$roomID", User ID: "$myUserId", Host ID: ${liveData.value.hostId}');

        // Special handling for invalid room ID error (1002001)
        if (result.errorCode == 1002001) {
          Loggers.error('❌ INVALID ROOM ID ERROR - Detailed analysis:');
          Loggers.error('   - Room ID format: "$roomID"');
          Loggers.error('   - Room ID length: ${roomID.length}');
          Loggers.error('   - Is Host: $isHost');

          if (!isHost) {
            Loggers.error('   - AUDIENCE ISSUE: Host may not have started streaming yet');
            Loggers.error('   - SOLUTION: Will attempt to create room as audience member');

            // For audience members, try a different approach
            // Sometimes ZEGO rooms need to be "created" by joining them
            if (retryCount < 3) {
              Loggers.info('🔄 Audience retry: Attempting room join with different strategy...');
              await Future.delayed(const Duration(seconds: 2));
              return loginRoom(retryCount: retryCount + 1);
            }
          }
        }

        // Retry logic for specific error codes
        if (_shouldRetryLogin(result.errorCode) && retryCount < 3) {
          Loggers.info('🔄 Retrying room login in 2 seconds...');
          await Future.delayed(const Duration(seconds: 2));
          return loginRoom(retryCount: retryCount + 1);
        }

        showSnackBar('Failed to join stream: $errorMessage');
        return result;
      }

      Loggers.success('Successfully logged into room: $roomID');

      if (isHost) {
        startHostPublish();
        return result;
      } else {
        // CRITICAL FIX: For audience members, immediately try to play the host's stream
        // The host's stream ID is the same as the room ID
        Loggers.info('🎥 Audience member logged in - attempting to play host stream: $roomID');

        // Small delay to ensure room is fully established
        Future.delayed(const Duration(milliseconds: 500), () {
          if (Get.isRegistered<LivestreamScreenController>(tag: roomID)) {
            _attemptToPlayHostStream(roomID);
          }
        });
      }

      final userRef = liveStreamUsersRef.doc(myUserId.toString());
      final stateRef = liveStreamUserStatesRef.doc(myUserId.toString());

      // Set user document if not exists
      if (!(await userRef.get()).exists) {
        final userModel = myUser.value?.appUser;
        if (userModel != null) await userRef.set(userModel.toJson());
      }

      // Fetch user state
      final stateSnap = await stateRef
          .withConverter(
            fromFirestore: (snapshot, _) =>
                LivestreamUserState.fromJson(snapshot.data()!),
            toFirestore: (value, _) => value.toJson(),
          )
          .get();

      if (!stateSnap.exists) {
        User? myUser = this.myUser.value;
        if (myUser != null) {
          final initialState = myUser.streamState(time: 0);
          await stateRef.set(initialState.toJson());
          _sendCommentToFirestore(type: LivestreamCommentType.joined);
        } else {
          Loggers.error('User not found');
        }
      } else {
        final state = stateSnap.data();
        if (state != null) {
          updateUserStateToFirestore(myUserId,
              type: state.type,
              audioStatus: state.audioStatus,
              videoStatus: state.videoStatus);
        }
      }

      updateLiveStreamData(watchingCount: 1);
      return result;
    } catch (e) {
      Loggers.error('Error in loginRoom: $e');

      // Retry on network or temporary errors
      if (retryCount < 2 && _isRetryableError(e)) {
        Loggers.info('Retrying room login due to error: $e');
        await Future.delayed(const Duration(seconds: 1));
        return loginRoom(retryCount: retryCount + 1);
      }

      showSnackBar('Unable to join the live stream. Please try again.');
      rethrow;
    }
  }

  String _getLoginErrorMessage(int errorCode) {
    switch (errorCode) {
      case 1002001:
        return 'Room login failed - Invalid room ID';
      case 1002002:
        return 'Room login failed - Room does not exist';
      case 1002003:
        return 'Room login failed - User already in room';
      case 1002004:
        return 'Room login failed - Room is full';
      case 1002005:
        return 'Room login failed - Network error';
      case 1002006:
        return 'Room login failed - Authentication failed';
      case 1002007:
        return 'Room login failed - Room has ended';
      default:
        return 'Room login failed - Error code: $errorCode';
    }
  }

  bool _shouldRetryLogin(int errorCode) {
    // Retry for network errors, temporary failures, but not for auth or room-specific errors
    return [1002005, 1002003].contains(errorCode); // Network error, already in room
  }

  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('timeout') ||
           errorString.contains('connection');
  }

  bool _isValidRoomID(String roomID) {
    // ZEGO room ID validation
    // - Must not be empty
    // - Must be alphanumeric (can include underscores and hyphens)
    // - Length should be reasonable (1-64 characters)
    if (roomID.isEmpty || roomID.length > 64) {
      return false;
    }

    // Check for valid characters (alphanumeric, underscore, hyphen)
    final validPattern = RegExp(r'^[a-zA-Z0-9_-]+$');
    return validPattern.hasMatch(roomID);
  }

  Future<bool> _validateStreamExists(String roomID) async {
    try {
      Loggers.info('🔍 Validating stream exists in Firebase for room: $roomID');
      final doc = await db.collection(FirebaseConst.liveStreams).doc(roomID).get();
      final exists = doc.exists;
      Loggers.info('📋 Stream exists in Firebase: $exists');
      return exists;
    } catch (e) {
      Loggers.error('❌ Error validating stream existence: $e');
      return false; // Assume stream doesn't exist on error
    }
  }

  /// CRITICAL FIX: Attempt to play host stream for audience members
  Future<void> _attemptToPlayHostStream(String roomID) async {
    try {
      Loggers.info('🎥 Attempting to play host stream for room: $roomID');

      // The host's stream ID is the same as the room ID
      final hostStreamID = roomID;

      // Check if we already have this stream view
      final existingStream = streamViews.firstWhereOrNull(
        (view) => view.streamId == hostStreamID
      );

      if (existingStream != null) {
        Loggers.info('✅ Host stream already exists in streamViews: $hostStreamID');
        return;
      }

      // Try to start playing the host's stream
      Loggers.info('🚀 Starting to play host stream: $hostStreamID');
      await startPlayStream(hostStreamID);

    } catch (e) {
      Loggers.error('❌ Failed to play host stream for room $roomID: $e');

      // Retry after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (Get.isRegistered<LivestreamScreenController>(tag: roomID)) {
          Loggers.info('🔄 Retrying to play host stream: $roomID');
          _attemptToPlayHostStream(roomID);
        }
      });
    }
  }

  void startListenEvent() async {
    // Callback for updates on the status of other users in the room.
    // Users can only receive callbacks when the isUserStatusNotify property of ZegoRoomConfig is set to `true` when logging in to the room (loginRoom).
    ZegoExpressEngine.onRoomUserUpdate =
        (roomID, updateType, List<ZegoUser> userList) {
      // Check if multiple users are in the room
      if (userList.length > 1) {
        // Force audio to speaker
        ZegoExpressEngine.instance.setAudioRouteToSpeaker(true);
      }
      Loggers.info(
          '👥 onRoomUserUpdate: roomID: $roomID, updateType: ${updateType.name}, userList: ${userList.map((e) => e.userID)}');
    };
    // Callback for updates on the status of the streams in the room.
    ZegoExpressEngine.onRoomStreamUpdate =
        (roomID, updateType, List<ZegoStream> streamList, extendedData) async {
      String priorityId = liveData.value.hostId.toString();

      streamList.sort((a, b) {
        if (a.streamID == priorityId) return -1; // a goes first
        if (b.streamID == priorityId) return 1; // b goes first
        return a.streamID.compareTo(b.streamID); // regular sorting
      });
      Loggers.info(
          '🎬 onRoomStreamUpdate: roomID: $roomID, updateType: $updateType, streamList: ${streamList.map((e) => e.streamID)}, extendedData: $extendedData');
      switch (updateType) {
        case ZegoUpdateType.Add:
          Loggers.info('➕ Adding ${streamList.length} streams to room $roomID');
          for (final stream in streamList) {
            Loggers.info('🎥 Starting playback for stream: ${stream.streamID}');
            startPlayStream(stream.streamID);
          }
          break;
        case ZegoUpdateType.Delete:
          for (final stream in streamList) {
            if (liveData.value.roomID == stream.streamID) {
              if (Get.isBottomSheetOpen == false) {
                Get.back();
              }
              for (var element in liveUsersStates) {
                if (element.type == LivestreamUserType.coHost) {
                  streamEnded();
                }
              }
              // Empty LiveData
              logoutRoom(1);
              stopListenEvent();
              liveData.value = Livestream();
            }
            streamViews
                .removeWhere((element) => element.streamId == stream.streamID);
            stopPlayStream(stream.streamID);
          }
          break;
      }
    };
    // Callback for updates on the current user's room connection status.
    ZegoExpressEngine.onRoomStateUpdate =
        (roomID, state, errorCode, extendedData) {
      Loggers.info(
          'onRoomStateUpdate: roomID: $roomID, state: ${state.name}, errorCode: $errorCode, extendedData: $extendedData');
    };

    // Callback for updates on the current user's stream publishing changes.
    ZegoExpressEngine.onPublisherStateUpdate =
        (streamID, state, errorCode, extendedData) {
      switch (state) {
        case ZegoPublisherState.NoPublish:
          streamViews.removeWhere((element) => element.streamId == streamID);
        case ZegoPublisherState.PublishRequesting:
        case ZegoPublisherState.Publishing:
      }
      debugPrint(
          'onPublisherStateUpdate: streamID: $streamID, state: ${state.name}, errorCode: $errorCode, extendedData: $extendedData');
    };
  }

  void stopListenEvent() {
    ZegoExpressEngine.onRoomUserUpdate = null;
    ZegoExpressEngine.onRoomStreamUpdate = null;
    ZegoExpressEngine.onRoomStateUpdate = null;
    ZegoExpressEngine.onPublisherStateUpdate = null;
  }

  Future<void> startHostPublish() async {
    try {
      if ((liveData.value.roomID ?? '').isEmpty) {
        Loggers.error('❌ No room ID found for host publishing');
        showSnackBar('Failed to start streaming: No room ID found');
        return;
      }

      String streamID = liveData.value.roomID ?? '';
      Loggers.info('🎬 Starting host publish for stream: $streamID');

      // Validate host preview exists
      if (hostPreview == null) {
        Loggers.error('❌ Host preview is null, cannot start publishing');
        showSnackBar('Camera preview not available. Please restart the stream.');
        return;
      }

      // Add host stream view
      final hostStreamView = StreamView(
          streamID, liveData.value.hostViewID ?? -1, hostPreview!, false);
      streamViews.add(hostStreamView);
      Loggers.info('✅ Host stream view added: $streamID (viewID: ${liveData.value.hostViewID})');

      // Configure audio settings
      await zegoEngine.mutePublishStreamAudio(false); // Ensure audio is not muted
      Loggers.info('🔊 Audio unmuted for host stream');

      // Start additional services
      startMinViewerTimeoutCheck(); //  Check time to Min. Viewers Required to continue live
      pushNotificationToFollowers(liveData.value);

      // CRITICAL: Start publishing the stream
      Loggers.info('🚀 Starting to publish stream: $streamID');
      await zegoEngine.startPublishingStream(streamID);
      Loggers.success('✅ Host publishing started successfully for stream: $streamID');

      // Verify stream is being published
      Future.delayed(const Duration(seconds: 1), () {
        _verifyStreamPublishing(streamID);
      });

    } catch (e) {
      Loggers.error('❌ Error starting host publish: $e');
      showSnackBar('Failed to start streaming. Please try again.');

      // Clean up on error
      try {
        await zegoEngine.stopPublishingStream();
      } catch (cleanupError) {
        Loggers.error('❌ Error during host publish cleanup: $cleanupError');
      }
    }
  }

  void _verifyStreamPublishing(String streamID) {
    Loggers.info('🔍 Verifying stream publishing status for: $streamID');
    // This is a placeholder for stream verification
    // In a real implementation, you might check ZEGO's stream status
  }

  Future<void> stopPublish() async {
    try {
      await zegoEngine.stopPublishingStream();
      Loggers.info('Publishing stream stopped successfully');
    } catch (e) {
      Loggers.error('Error stopping publish stream: $e');
      // Don't show user error for stop operation as it might be called during cleanup
    }
  }

  Future<void> startPlayStream(String streamID, {int retryCount = 0}) async {
    Loggers.info('🎬 Starting to play stream: $streamID (attempt ${retryCount + 1})');

    // Check if stream already exists
    final existingStream = streamViews.firstWhereOrNull(
      (view) => view.streamId == streamID
    );
    if (existingStream != null) {
      Loggers.info('✅ Stream already playing: $streamID');
      return;
    }

    int streamViewId = -1;
    try {
      await zegoEngine.createCanvasView((viewID) {
        Loggers.info('🖼️ Created remote view with ID: $viewID for stream: $streamID');
        streamViewId = viewID;
        ZegoCanvas canvas =
            ZegoCanvas(viewID, viewMode: ZegoViewMode.AspectFill);
        ZegoPlayerConfig config = ZegoPlayerConfig.defaultConfig();
        config.resourceMode =
            ZegoStreamResourceMode.Default; // live streaming (CDN)

        Loggers.info(
            '🎥 StartPlayStream config - StreamID: $streamID, ViewID: $viewID, ViewMode: ${canvas.viewMode}, ResourceMode: ${config.resourceMode}');

        zegoEngine.startPlayingStream(streamID, canvas: canvas, config: config);
      }).then((canvasViewWidget) {
        if (canvasViewWidget != null) {
          final streamView = StreamView(streamID, streamViewId, canvasViewWidget, false);
          streamViews.add(streamView);
          Loggers.success('✅ Stream view added to streamViews: $streamID (total views: ${streamViews.length})');
        } else {
          Loggers.error('❌ Canvas view widget is null for stream: $streamID');
        }
      });
    } catch (e, stackTrace) {
      Loggers.error('❌ Failed to start playing stream $streamID: $e');
      Loggers.error('StackTrace: $stackTrace');

      // Retry logic for stream playback
      if (retryCount < 3) {
        Loggers.info('🔄 Retrying stream playback in 2 seconds...');
        Future.delayed(const Duration(seconds: 2), () {
          if (Get.isRegistered<LivestreamScreenController>(tag: liveData.value.roomID)) {
            startPlayStream(streamID, retryCount: retryCount + 1);
          }
        });
      } else {
        Loggers.error('❌ Max retries reached for stream: $streamID');
      }
    }
  }

  Future<void> stopPlayStream(String streamID) async {
    Loggers.info('Stopping playback for stream: $streamID');

    try {
      zegoEngine.stopPlayingStream(streamID);
      Loggers.success('Stopped playing stream: $streamID');

      StreamView? stream = streamViews
          .firstWhereOrNull((element) => element.streamId == streamID);

      if (stream?.streamViewId != null) {
        Loggers.info('Destroying remote view with ID: ${stream?.streamViewId}');
        await zegoEngine.destroyCanvasView(stream!.streamViewId);
        Loggers.success('Remote view destroyed successfully.');
      }
    } catch (e, stackTrace) {
      Loggers.error('Failed to stop playing stream: $e');
      Loggers.error('StackTrace: $stackTrace');
    }
  }

  Future<void> stopPreview({int? viewId}) async {
    try {
      Loggers.info('Stopping preview...');
      zegoEngine.stopPreview();

      int id = viewId ?? -1;
      if (id != -1) {
        await zegoEngine.destroyCanvasView(id);
        Loggers.info('Canvas view destroyed: $id');
      }

      Loggers.success('Preview stopped successfully');
    } catch (e) {
      Loggers.error('Error stopping preview: $e');
      // Don't rethrow as this is cleanup code
    }
  }

  Future<void> updateLiveStreamData({
    BattleType? battleType,
    LivestreamType? type,
    int? battleCreatedAt,
    int? battleDuration,
    int watchingCount = 0,
    FieldValue? coHostId,
  }) async {
    bool isExist = (await liveStreamDocRef.get()).exists;
    if (!isExist) return;

    liveStreamDocRef.update({
      if (battleType != null) FirebaseConst.battleType: battleType.value,
      if (type != null) FirebaseConst.type: type.value,
      if (battleCreatedAt != null)
        FirebaseConst.battleCreatedAt: battleCreatedAt,
      if (battleDuration != null) FirebaseConst.battleDuration: battleDuration,
      if (watchingCount != 0)
        FirebaseConst.watchingCount: FieldValue.increment(watchingCount),
      if (coHostId != null) FirebaseConst.coHostIds: coHostId
    });
  }

  void handleRequestResponse({
    required AppUser? user,
    required bool isRefused,
    LivestreamComment? comment,
  }) {
    final userId = user?.userId;
    if (userId == null) return;

    // Update user state based on refusal
    updateUserStateToFirestore(userId,
        type: isRefused
            ? LivestreamUserType.audience
            : LivestreamUserType.coHost);

    // Determine the comment to delete
    final commentToDelete = comment ??
        comments.firstWhereOrNull((element) =>
            element.senderId == userId &&
            element.commentType == LivestreamCommentType.request);

    if (commentToDelete != null) {
      liveStreamCommentsRef.doc(commentToDelete.id.toString()).delete();
    }
  }

  Future<void> deleteStreamOnFirebase() async {
    final String? roomId = liveData.value.roomID;

    if (roomId == null) {
      Loggers.error('Room ID is null. Cannot stop live stream.');
      return;
    }

    Loggers.info('Stopping live stream Room : $roomId');

    try {
      // Get all users in the livestream and delete them
      QuerySnapshot usersSnapshot = await liveStreamUserStatesRef.get();

      WriteBatch batch = db.batch();

      for (var doc in usersSnapshot.docs) {
        batch.delete(doc.reference);
      }

      Loggers.info(
          'Deleted ${usersSnapshot.docs.length} livestream users_state.');

      // Get all Comments in the livestream and delete them
      QuerySnapshot commentsSnapshot = await liveStreamCommentsRef.get();

      for (var doc in commentsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      Loggers.info(
          'Deleted ${commentsSnapshot.docs.length} livestream comments.');

      // Delete the main live stream document
      batch.delete(liveStreamDocRef);

      // Commit batch delete
      await batch.commit();
      Loggers.success(
          'livestream , users_states , comments  deleted from Firestore.');
    } catch (e, stackTrace) {
      Loggers.error('Failed to stop live stream: $e');
      Loggers.error('StackTrace: $stackTrace');
    }
  }

  void listenLiveStreamData() {
    int likeCount = liveData.value.likeCount ?? 0;
    // liveStreamDocListener?.cancel();
    liveStreamDocListener = liveStreamDocRef
        .withConverter<Livestream>(
          fromFirestore: (snapshot, _) {
            if (!snapshot.exists) {
              Loggers.error(
                  'Livestream document not found for Room ID: ${liveData.value.roomID}');
              return Livestream(); // default instance
            }
            return Livestream.fromJson(snapshot.data()!);
          },
          toFirestore: (value, _) => value.toJson(),
        )
        .snapshots()
        .listen(
      (event) async {
        final stream = event.data();
        if (stream == null) {
          Loggers.warning('Livestream data is null');
          return;
        }

        if (stream.battleType == BattleType.initiate) {
          timer?.cancel();
          remainingBattleSeconds.value =
              Duration(minutes: stream.battleDuration).inSeconds;
          countdownPlayer.pause();
        }

        if (stream.battleType == BattleType.waiting) {
          totalBattleSecond =
              Duration(minutes: stream.battleDuration).inSeconds;
        }

        // Update LiveData
        liveData.value = stream;

        // Trigger like animation if changed
        final newLikeCount = stream.likeCount ?? 0;
        if (likeCount != newLikeCount) {
          onLikeTap?.call();
          likeCount = newLikeCount;
        }
      },
      onError: (error) =>
          Loggers.error('Error listening to livestream: $error'),
    );
  }

  void listenUserState() {
    // Cancel any existing listener
    liveStreamUserStatesListener?.cancel();
    // Loggers.info('👂 Listening to live stream users state...');

    liveStreamUserStatesListener = liveStreamUserStatesRef
        .withConverter(
          fromFirestore: (snapshot, options) {
            if (!snapshot.exists) return null;
            return LivestreamUserState.fromJson(snapshot.data()!);
          },
          toFirestore: (value, options) {
            if (value == null) return {};
            return value.toJson();
          },
        )
        .snapshots()
        .listen((event) {
          // Loggers.info(
          //     '📦 Firestore snapshot received with ${event.docChanges.length} changes');

          for (var change in event.docChanges) {
            final state = change.doc.data();
            if (state == null) {
              // Loggers.warning('⚠️ Null state found in change, skipping...');
              continue;
            }

            switch (change.type) {
              case DocumentChangeType.added:
                _showJoinStreamSheet(state);
                liveUsersStates.add(state);
                // Loggers.info('➕ User added: ${state.userId}');
                break;

              case DocumentChangeType.modified:
                LivestreamUserState? oldState =
                    liveUsersStates.firstWhereOrNull(
                        (element) => element.userId == state.userId);
                updateStateAction(oldState, state);
                liveUsersStates.removeWhere((u) => u.userId == state.userId);
                liveUsersStates.add(state);
                // Loggers.info('🔁 User modified: ${state.userId}');
                break;

              case DocumentChangeType.removed:
                liveUsersStates.removeWhere((u) => u.userId == state.userId);
                // Loggers.info('➖ User removed: ${state.userId}');
                break;
            }
          }
          requestList.value = liveUsersStates
              .where((element) => element.type == LivestreamUserType.requested)
              .toList();
          audienceList.value = liveUsersStates
              .where((element) =>
                  element.type != LivestreamUserType.host &&
                  element.type != LivestreamUserType.left)
              .toList();
          invitedList.value = liveUsersStates
              .where((element) => element.type == LivestreamUserType.invited)
              .toList();
          coHostList.value = liveUsersStates
              .where((element) => element.type == LivestreamUserType.coHost)
              .toList();
          audienceMemberList.value = liveUsersStates
              .where((element) =>
                  element.type != LivestreamUserType.left &&
                  element.userId != myUserId)
              .toList();
        });
  }

  void fetchLiveStreamComments() {
    Loggers.info('Fetching live stream comments...');
    liveStreamCommentsListener?.cancel();
    liveStreamCommentsListener = liveStreamCommentsRef
        .withConverter(
          fromFirestore: (snapshot, options) {
            if (!snapshot.exists) {
              Loggers.error('No comments found in Firestore.');
              return null;
            }
            return LivestreamComment.fromJson(snapshot.data()!);
          },
          toFirestore: (value, options) => value?.toJson() ?? {},
        )
        .snapshots()
        .listen((querySnapshot) {
      // Loggers.info(
      //     'Received comment changes: ${querySnapshot.docChanges.length}');

      for (var change in querySnapshot.docChanges) {
        LivestreamComment? comment = change.doc.data();
        if (comment == null) {
          Loggers.error('Null comment received.');
          continue;
        }

        switch (change.type) {
          case DocumentChangeType.added:
            if (comment.commentType == LivestreamCommentType.request &&
                !isHost) {
              continue;
            }
            comments.add(comment);
            // Loggers.info('New comment added: ${comment.toJson()}');
            break;

          case DocumentChangeType.modified:
            if (comment.commentType == LivestreamCommentType.request &&
                !isHost) {
              return;
            }
            int index = comments.indexWhere((c) => c.id == comment.id);
            if (index != -1) {
              comments[index] = comment;
              // Loggers.info('Comment modified: ${comment.toJson()}');
            }
            break;

          case DocumentChangeType.removed:
            comments.removeWhere((c) => c.id == comment.id);
            // Loggers.info('Comment removed: ${comment.id}');
            break;
        }
      }

      // Assign sender and receiver users to comments
      for (var comment in comments) {
        comment.gift =
            gifts.firstWhereOrNull((gift) => gift.id == comment.giftId);
      }

      comments.sort((a, b) => (b.id ?? 0).compareTo(a.id ?? 0));
    });
  }

  void toggleCamera() async {
    try {
      Loggers.info('Starting camera toggle operation...');

      // Check if camera is currently enabled before attempting to flip
      final currentUserId = myUserId;
      final userState = liveUsersStates.firstWhereOrNull(
          (element) => element.userId == currentUserId);

      Loggers.info('Current user state - Video Status: ${userState?.videoStatus}');

      if (userState?.videoStatus == VideoAudioStatus.offByMe ||
          userState?.videoStatus == VideoAudioStatus.offByHost) {
        Loggers.warning('Camera toggle blocked - camera is disabled');
        showSnackBar('Please enable camera first before rotating.');
        return;
      }

      // Ensure camera is enabled before rotation
      Loggers.info('Ensuring camera is ready for toggle...');
      await _ensureCameraIsReady();

      final previousState = isFrontCamera;
      isFrontCamera = !isFrontCamera;

      Loggers.info('Calling ZegoEngine.useFrontCamera with: $isFrontCamera');
      zegoEngine.useFrontCamera(isFrontCamera, channel: ZegoPublishChannel.Main);

      Loggers.info('Camera toggled successfully from ${previousState ? 'front' : 'back'} to ${isFrontCamera ? 'front' : 'back'}');
    } catch (e) {
      Loggers.error('Error toggling camera: $e');
      showSnackBar('Failed to toggle camera. Please try again.');
      // Revert the state if toggle failed
      isFrontCamera = !isFrontCamera;
      Loggers.info('Camera state reverted due to error');
    }
  }

  void toggleFlipCamera() async {
    try {
      Loggers.info('Starting camera flip operation...');

      // Check if camera is currently enabled before attempting to flip
      final currentUserId = myUserId;
      final userState = liveUsersStates.firstWhereOrNull(
          (element) => element.userId == currentUserId);

      Loggers.info('Current user state - Video Status: ${userState?.videoStatus}');

      if (userState?.videoStatus == VideoAudioStatus.offByMe ||
          userState?.videoStatus == VideoAudioStatus.offByHost) {
        Loggers.warning('Camera flip blocked - camera is disabled');
        showSnackBar('Please enable camera first before flipping.');
        return;
      }

      // Ensure camera is enabled and ready before rotation
      Loggers.info('Ensuring camera is ready for flip...');
      await _ensureCameraIsReady();

      final previousState = isFrontCamera;
      isFrontCamera = !isFrontCamera;

      Loggers.info('Calling ZegoEngine.useFrontCamera with: $isFrontCamera');
      zegoEngine.useFrontCamera(isFrontCamera, channel: ZegoPublishChannel.Main);

      Loggers.info('Camera flipped successfully from ${previousState ? 'front' : 'back'} to ${isFrontCamera ? 'front' : 'back'}');
    } catch (e) {
      Loggers.error('Error flipping camera: $e');
      showSnackBar('Failed to flip camera. Please try again.');
      // Revert the state if flip failed
      isFrontCamera = !isFrontCamera;
      Loggers.info('Camera state reverted due to error');
    }
  }

  /// Ensures camera is ready for operations like rotation
  Future<void> _ensureCameraIsReady() async {
    try {
      // Wait if camera is currently initializing
      if (isCameraInitializing.value) {
        Loggers.info('Waiting for camera initialization to complete...');
        while (isCameraInitializing.value) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
      }

      // Get current user state from Firestore (source of truth)
      final currentUserId = myUserId;
      final userState = liveUsersStates.firstWhereOrNull(
          (element) => element.userId == currentUserId);

      // Check if camera is enabled according to Firestore state
      bool isCameraEnabledInFirestore = userState?.videoStatus == VideoAudioStatus.on;

      if (!isCameraEnabledInFirestore) {
        throw Exception('Camera is not enabled in user state');
      }

      // Sync local state with Firestore state
      if (isCameraEnabled.value != isCameraEnabledInFirestore) {
        Loggers.info('Syncing local camera state with Firestore state');
        isCameraEnabled.value = isCameraEnabledInFirestore;
      }

      // Ensure camera is actually enabled at the engine level
      await zegoEngine.enableCamera(true);

      // Small delay to ensure camera is fully ready for rotation
      await Future.delayed(const Duration(milliseconds: 150));

      Loggers.info('Camera readiness verified for rotation');
    } catch (e) {
      Loggers.error('Error ensuring camera readiness: $e');
      rethrow;
    }
  }

  void toggleMic(LivestreamUserState? state) async {
    if (state?.audioStatus == VideoAudioStatus.offByHost) {
      return showSnackBar(LKey.theHostHasTurnedOffYourAudio);
    }

    bool isAudioOn = state?.audioStatus == VideoAudioStatus.on;

    try {
      if (isAudioOn) {
        // Turn off microphone
        updateUserStateToFirestore(myUserId,
            audioStatus: VideoAudioStatus.offByMe);
        zegoEngine.muteMicrophone(true); // true = mute
        Loggers.info('Microphone disabled for user: $myUserId');
      } else {
        // Turn on microphone
        updateUserStateToFirestore(myUserId, audioStatus: VideoAudioStatus.on);
        zegoEngine.muteMicrophone(false); // false = unmute
        Loggers.info('Microphone enabled for user: $myUserId');
      }
    } catch (e) {
      Loggers.error('Error toggling microphone: $e');
      showSnackBar('Failed to toggle microphone. Please check microphone permissions.');

      // Revert Firestore state if microphone operation failed
      try {
        if (isAudioOn) {
          // If we were trying to turn off mic but failed, revert to on
          updateUserStateToFirestore(myUserId, audioStatus: VideoAudioStatus.on);
        } else {
          // If we were trying to turn on mic but failed, revert to off
          updateUserStateToFirestore(myUserId, audioStatus: VideoAudioStatus.offByMe);
        }
      } catch (revertError) {
        Loggers.error('Error reverting microphone state: $revertError');
      }
    }
  }

  void toggleVideo(LivestreamUserState? state) async {
    if (state?.videoStatus == VideoAudioStatus.offByHost) {
      return showSnackBar(LKey.theHostHasTurnedOffYourVideo.tr);
    }

    bool isVideoOn = state?.videoStatus == VideoAudioStatus.on;

    try {
      if (isVideoOn) {
        // Turn off video
        Loggers.info('Disabling video for user: $myUserId');
        lastCameraToggleTime = DateTime.now();

        updateUserStateToFirestore(myUserId,
            videoStatus: VideoAudioStatus.offByMe);
        await zegoEngine.enableCamera(false);

        // Update local state
        isCameraEnabled.value = false;

        Loggers.info('Video disabled successfully for user: $myUserId');
      } else {
        // Turn on video - need to re-initialize camera properly
        Loggers.info('Enabling video for user: $myUserId');

        // Prevent rapid toggling
        if (lastCameraToggleTime != null) {
          final timeSinceLastToggle = DateTime.now().difference(lastCameraToggleTime!);
          if (timeSinceLastToggle.inMilliseconds < 500) {
            Loggers.warning('Camera toggle too rapid, waiting...');
            await Future.delayed(Duration(milliseconds: 500 - timeSinceLastToggle.inMilliseconds));
          }
        }

        // First update Firestore state
        updateUserStateToFirestore(myUserId, videoStatus: VideoAudioStatus.on);

        // Re-enable camera with proper initialization
        await _reinitializeCamera();

        // Verify state consistency after re-initialization
        if (!_isCameraStateConsistent()) {
          Loggers.warning('Camera state inconsistency detected, attempting recovery...');
          await _recoverCameraState();
        }

        Loggers.info('Video enabled successfully for user: $myUserId');
      }
    } catch (e) {
      Loggers.error('Error toggling video: $e');
      showSnackBar('Failed to toggle video. Please check camera permissions.');

      // Revert Firestore state if camera operation failed
      try {
        if (isVideoOn) {
          // If we were trying to turn off video but failed, revert to on
          updateUserStateToFirestore(myUserId, videoStatus: VideoAudioStatus.on);
        } else {
          // If we were trying to turn on video but failed, revert to off
          updateUserStateToFirestore(myUserId, videoStatus: VideoAudioStatus.offByMe);
        }
      } catch (revertError) {
        Loggers.error('Error reverting video state: $revertError');
      }
    }
  }

  /// Properly re-initializes camera after it has been disabled
  Future<void> _reinitializeCamera() async {
    if (isCameraInitializing.value) {
      Loggers.warning('Camera initialization already in progress, waiting...');
      // Wait for current initialization to complete
      while (isCameraInitializing.value) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
      return;
    }

    try {
      isCameraInitializing.value = true;
      lastCameraToggleTime = DateTime.now();

      Loggers.info('Starting camera re-initialization...');

      // Enable camera first
      await zegoEngine.enableCamera(true);

      // Wait a moment for camera to initialize
      await Future.delayed(const Duration(milliseconds: 200));

      // Set camera orientation based on current state
      zegoEngine.useFrontCamera(isFrontCamera, channel: ZegoPublishChannel.Main);

      // Update local state
      isCameraEnabled.value = true;

      // If we're the host and have a preview, ensure it's active
      if (isHost && hostPreview != null) {
        Loggers.info('Camera re-initialized for host with existing preview');
      }

      Loggers.info('Camera re-initialization completed successfully');
    } catch (e) {
      Loggers.error('Error during camera re-initialization: $e');
      isCameraEnabled.value = false;
      rethrow; // Re-throw to be handled by caller
    } finally {
      isCameraInitializing.value = false;
    }
  }

  /// Recovers camera state in case of errors or inconsistencies
  Future<void> _recoverCameraState() async {
    try {
      Loggers.info('Attempting camera state recovery...');

      // Get current user state from Firestore
      final currentUserId = myUserId;
      final userState = liveUsersStates.firstWhereOrNull(
          (element) => element.userId == currentUserId);

      if (userState?.videoStatus == VideoAudioStatus.on) {
        // Camera should be enabled
        if (!isCameraEnabled.value) {
          Loggers.info('Recovering enabled camera state...');
          await _reinitializeCamera();
        }
      } else {
        // Camera should be disabled
        if (isCameraEnabled.value) {
          Loggers.info('Recovering disabled camera state...');
          await zegoEngine.enableCamera(false);
          isCameraEnabled.value = false;
        }
      }

      Loggers.info('Camera state recovery completed');
    } catch (e) {
      Loggers.error('Error during camera state recovery: $e');
    }
  }

  /// Validates camera state consistency
  bool _isCameraStateConsistent() {
    final currentUserId = myUserId;
    final userState = liveUsersStates.firstWhereOrNull(
        (element) => element.userId == currentUserId);

    if (userState == null) return true; // No state to validate

    final shouldBeEnabled = userState.videoStatus == VideoAudioStatus.on;
    return shouldBeEnabled == isCameraEnabled.value;
  }

  /// Initializes camera state tracking based on current user state
  void _initializeCameraState() {
    try {
      // Get current user state
      final currentUserId = myUserId;
      final userState = liveUsersStates.firstWhereOrNull(
          (element) => element.userId == currentUserId);

      // Initialize camera state based on Firestore state
      bool shouldBeEnabled = userState?.videoStatus == VideoAudioStatus.on;

      // If no user state yet, assume camera is enabled for host, disabled for audience
      if (userState == null) {
        shouldBeEnabled = isHost;
      }

      isCameraEnabled.value = shouldBeEnabled;

      Loggers.info('Camera state initialized: ${shouldBeEnabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      Loggers.error('Error initializing camera state: $e');
      // Default to enabled for host, disabled for audience
      isCameraEnabled.value = isHost;
    }
  }

  /// Syncs local camera state with user state from Firestore
  void _syncCameraStateWithUserState(LivestreamUserState userState) {
    try {
      bool shouldBeEnabled = userState.videoStatus == VideoAudioStatus.on;

      if (isCameraEnabled.value != shouldBeEnabled) {
        Loggers.info('Syncing camera state: ${isCameraEnabled.value} -> $shouldBeEnabled');
        isCameraEnabled.value = shouldBeEnabled;
      }
    } catch (e) {
      Loggers.error('Error syncing camera state with user state: $e');
    }
  }

  void toggleStreamAudio(int? streamId) {
    StreamView? view = streamViews
        .firstWhereOrNull((element) => int.parse(element.streamId) == streamId);

    zegoEngine.mutePlayStreamAudio(
        '$streamId', (view?.isMuted ?? false) ? false : true);
    view?.isMuted = view.isMuted ? false : true;
    if (view != null) {
      streamViews[streamViews.indexWhere(
          (element) => int.parse(element.streamId) == streamId)] = view;
      streamViews.refresh();
    }
  }

  void onLikeButtonTap() async {
    bool isExist = (await liveStreamDocRef.get()).exists;
    if (isExist) {
      HapticManager.shared.light();
      liveStreamDocRef
          .update({FirebaseConst.likeCount: FieldValue.increment(1)});
    }
  }

  void onTextCommentSend() {
    String comment = textCommentController.text.trim();
    textCommentController.clear();
    isTextEmpty.value = true;
    if (comment.isEmpty) return;
    _sendCommentToFirestore(type: LivestreamCommentType.text, comment: comment);
  }

  void onGiftTap(GiftType type,
      {BattleView battleViewType = BattleView.red,
      List<AppUser> users = const []}) {
    users.removeWhere((element) => element.userId == myUserId);
    if (liveData.value.type == LivestreamType.battle &&
        liveData.value.battleType == BattleType.end) {
      return showSnackBar(LKey.battleEndedGiftNotSent.tr);
    }
    GiftManager.openGiftSheet(
        onCompletion: (giftManager) {
          Gift gift = giftManager.gift;
          AppUser? user = giftManager.streamUser;

          int coinPrice = gift.coinPrice?.toInt() ?? 0;

          _sendCommentToFirestore(
              type: LivestreamCommentType.gift,
              giftId: gift.id,
              receiverId: user?.userId);
          updateUserStateToFirestore(
            user?.userId,
            battleCoin: type == GiftType.battle ? coinPrice : null,
            currentBattleCoin: type == GiftType.battle ? coinPrice : null,
            liveCoin: type == GiftType.livestream ? coinPrice : null,
          );
        },
        giftType: type,
        battleViewType: battleViewType,
        streamUsers: users);
  }

  _sendCommentToFirestore(
      {required LivestreamCommentType type,
      String? comment,
      int? giftId,
      int? receiverId}) async {
    int time = DateTime.now().millisecondsSinceEpoch;
    try {
      await _addUsersFirebaseFireStore();
      liveStreamCommentsRef.doc('$time').set(LivestreamComment(
              comment: comment,
              commentType: type,
              id: time,
              senderId: myUserId,
              receiverId: receiverId,
              giftId: giftId)
          .toJson());
    } catch (e) {
      Loggers.error('Message Error : $e');
    }
  }

  Future<void> _addUsersFirebaseFireStore() async {
    DocumentReference myUserRef = liveStreamUsersRef.doc(myUserId.toString());

    DocumentSnapshot isMyUserExist = await myUserRef.get();
    if (myUser.value != null) {
      if (isMyUserExist.exists) {
        myUserRef.update(myUser.value!.appUser.toJson());
      } else {
        myUserRef.set(myUser.value?.appUser.toJson());
      }
    }
  }

  void onVideoRequestSend(Livestream liveData) {
    LivestreamUserState? state = liveUsersStates
        .firstWhereOrNull((element) => element.userId == myUserId);
    switch (state?.type) {
      case null:
        break;
      case LivestreamUserType.audience:
        updateUserStateToFirestore(myUserId,
            type: LivestreamUserType.requested);
        _sendCommentToFirestore(
            type: LivestreamCommentType.request, receiverId: liveData.hostId);
        showSnackBar(LKey.requestJoinToHost.tr);
        break;
      case LivestreamUserType.requested:
        showSnackBar(LKey.joinRequestSentDescription.tr);
        break;
      case LivestreamUserType.host:
      case LivestreamUserType.coHost:
      case LivestreamUserType.invited:
      case LivestreamUserType.left:
        break;
    }
  }

  Future<void> updateUserStateToFirestore(
    int? userId, {
    LivestreamUserType? type,
    VideoAudioStatus? audioStatus,
    VideoAudioStatus? videoStatus,
    int? battleCoin,
    int? liveCoin,
    bool? isFollow,
    int? joinTime,
    int? currentBattleCoin,
  }) async {
    if (userId == null) {
      Loggers.error('updateUserStateToFirestore: userId is null');
      return;
    }

    DocumentReference reference =
        liveStreamUserStatesRef.doc(userId.toString());
    bool isExist = (await reference.get()).exists;
    if (!isExist) {
      Loggers.error('updateUserStateToFirestore Not Found $userId');
      return;
    }

    try {
      final updateData = <String, dynamic>{
        if (type != null) FirebaseConst.type: type.value,
        if (audioStatus != null) FirebaseConst.audioStatus: audioStatus.value,
        if (videoStatus != null) FirebaseConst.videoStatus: videoStatus.value,
        if (battleCoin != null)
          FirebaseConst.totalBattleCoin:
              battleCoin == 0 ? 0 : FieldValue.increment(battleCoin),
        if (currentBattleCoin != null)
          FirebaseConst.currentBattleCoin: currentBattleCoin == 0
              ? 0
              : FieldValue.increment(currentBattleCoin),
        if (liveCoin != null)
          FirebaseConst.liveCoin:
              liveCoin == 0 ? 0 : FieldValue.increment(liveCoin),
        if (isFollow != null)
          FirebaseConst.followersGained: isFollow
              ? FieldValue.arrayUnion([myUserId])
              : FieldValue.arrayRemove([myUserId]),
        if (joinTime != null) FirebaseConst.joinStreamTime: joinTime,
      };
      if (battleCoin != null || liveCoin != null) {
        myUser.value?.coinEstimatedValue(
            battleCoin?.toDouble() ?? liveCoin?.toDouble());
        SessionManager.instance.setUser(myUser.value);
      }
      await liveStreamUserStatesRef.doc(userId.toString()).update(updateData);
      Loggers.success('User state updated for userId: $userId');
    } catch (e, stack) {
      Loggers.error('Failed to update user state: $e\n$stack');
    }
  }

  void onInvite(AppUser? user, {bool isInvited = false}) {
    updateUserStateToFirestore(user?.userId,
        type: isInvited
            ? LivestreamUserType.audience
            : LivestreamUserType.invited);
  }

  void _showJoinStreamSheet(LivestreamUserState state) {
    if (state.userId == myUserId && state.type == LivestreamUserType.invited) {
      AppUser? hostUser = liveData.value.getHostUser(firestoreController.users);
      isJoinSheetOpen = true;
      Get.bottomSheet(
              LiveStreamJoinSheet(
                  hostUser: hostUser,
                  myUser: myUser.value,
                  onJoined: () async {
                    LivestreamUserState? userState =
                        liveUsersStates.firstWhereOrNull(
                            (element) => element.userId == myUserId);
                    if (userState?.type == LivestreamUserType.invited) {
                      updateUserStateToFirestore(myUserId,
                          type: LivestreamUserType.coHost);
                    } else {
                      showSnackBar(LKey.joinCancelledDescription.tr);
                    }
                  },
                  onCancel: () {
                    updateUserStateToFirestore(myUserId,
                        type: LivestreamUserType.audience);
                  }),
              isScrollControlled: true,
              enableDrag: false,
              isDismissible: false)
          .then(
        (value) {
          isJoinSheetOpen = false;
        },
      );
    }
  }

  void publishCoHostStream(int streamId) async {
    try {
      bool isPermissionGranted = await requestPermission();
      if (isPermissionGranted) {
        int canvasViewID = -1;

        // ✅ Enable camera and microphone
        await zegoEngine.enableCamera(true);
        await zegoEngine.mutePublishStreamAudio(false);
        zegoEngine.muteMicrophone(false);

        // ✅ Create preview canvas and start preview
        await zegoEngine.createCanvasView((viewID) async {
          canvasViewID = viewID;
          ZegoCanvas previewCanvas =
              ZegoCanvas(canvasViewID, viewMode: ZegoViewMode.AspectFill);
          zegoEngine.startPreview(canvas: previewCanvas);
        }).then((canvasViewWidget) {
          if (canvasViewWidget != null) {
            streamViews.add(
              StreamView('$streamId', canvasViewID, canvasViewWidget, false),
            );
          }
        });

        // ✅ Publish the stream
        await zegoEngine.startPublishingStream('$streamId');

        // ✅ Force audio output to speaker (after stream starts)
        Future.delayed(const Duration(milliseconds: 300), () {
          zegoEngine.setAudioRouteToSpeaker(true);
        });

        updateLiveStreamData(coHostId: FieldValue.arrayUnion([streamId]));
        _sendCommentToFirestore(type: LivestreamCommentType.joinedCoHost);
        updateUserStateToFirestore(myUserId,
            joinTime: DateTime.now().millisecondsSinceEpoch);

        Loggers.info('Co-host stream published successfully for stream ID: $streamId');
      } else {
        Get.bottomSheet(ConfirmationSheet(
          title: LKey.cameraMicrophonePermissionTitle.tr,
          description: LKey.cameraMicrophonePermissionDescription.tr,
          onTap: openAppSettings,
        ));
      }
    } catch (e) {
      Loggers.error('Error publishing co-host stream: $e');
      showSnackBar('Failed to start co-host stream. Please try again.');

      // Clean up any partial state
      try {
        await zegoEngine.stopPublishingStream();
      } catch (cleanupError) {
        Loggers.error('Error during cleanup: $cleanupError');
      }
    }
  }

  Future<bool> requestPermission() async {
    Loggers.info("requestPermission...");
    try {
      PermissionStatus microphoneStatus = await Permission.microphone.request();
      if (microphoneStatus != PermissionStatus.granted) {
        Loggers.error('Error: Microphone permission not granted!!!');
        return false;
      }
    } on Exception catch (error) {
      Loggers.error("[ERROR], request microphone permission exception, $error");
    }

    try {
      PermissionStatus cameraStatus = await Permission.camera.request();
      if (cameraStatus != PermissionStatus.granted) {
        Loggers.error('Error: Camera permission not granted!!!');
        return false;
      }
    } on Exception catch (error) {
      Loggers.error("[ERROR], request camera permission exception, $error");
    }

    return true;
  }

  Future<void> closeCoHostStream(int? streamId) async {
    StreamView? view = streamViews
        .firstWhereOrNull((element) => element.streamId == '$streamId');
    if (view != null) {
      await stopPreview(viewId: view.streamViewId);
      await stopPublish();
      updateLiveStreamData(coHostId: FieldValue.arrayRemove([streamId]));
      LivestreamComment? comment = comments.firstWhereOrNull((element) =>
          element.senderId == myUserId &&
          element.commentType == LivestreamCommentType.joinedCoHost);
      if (comment != null) {
        liveStreamCommentsRef.doc(comment.id.toString()).delete();
      }
      updateUserStateToFirestore(streamId,
          type: LivestreamUserType.audience,
          audioStatus: VideoAudioStatus.offByMe,
          videoStatus: VideoAudioStatus.offByMe,
          battleCoin: 0,
          currentBattleCoin: 0);
      streamViews.removeWhere((element) => element.streamId == '$streamId');
      stopPlayStream(streamId.toString());
      streamEnded();
    }
  }

  void coHostVideoToggle(LivestreamUserState state) {
    if (state.videoStatus == VideoAudioStatus.offByMe) {
      return showSnackBar(LKey.theCoHostHasTurnedOffTheirVideo);
    }

    updateUserStateToFirestore(state.userId,
        videoStatus: state.videoStatus == VideoAudioStatus.on
            ? VideoAudioStatus.offByHost
            : VideoAudioStatus.on);
  }

  void coHostAudioToggle(LivestreamUserState state) {
    if (state.audioStatus == VideoAudioStatus.offByMe) {
      return showSnackBar(LKey.theCoHostHasTurnedOffTheirAudio);
    }

    updateUserStateToFirestore(state.userId,
        audioStatus: state.audioStatus == VideoAudioStatus.on
            ? VideoAudioStatus.offByHost
            : VideoAudioStatus.on);
  }

  void updateStateAction(
      LivestreamUserState? oldState, LivestreamUserState newState) async {
    if (newState.userId == myUserId) {
      Loggers.info('Updating state for userId: ${newState.toJson()}');

      // Sync camera state when our user state changes
      _syncCameraStateWithUserState(newState);
      if (newState.type == LivestreamUserType.coHost &&
          oldState?.type != LivestreamUserType.coHost) {
        publishCoHostStream(myUserId);
      }

      if (newState.type == LivestreamUserType.audience &&
          oldState?.type == LivestreamUserType.invited &&
          isJoinSheetOpen) {
        Get.back();
      }

      if (newState.type == LivestreamUserType.invited &&
          oldState?.type == LivestreamUserType.audience) {
        _showJoinStreamSheet(newState);
      }
      if (oldState?.videoStatus != newState.videoStatus) {
        try {
          bool isVideoOn = newState.videoStatus == VideoAudioStatus.on;
          await zegoEngine.enableCamera(isVideoOn);

          // If enabling camera, ensure proper initialization
          if (isVideoOn) {
            zegoEngine.useFrontCamera(isFrontCamera, channel: ZegoPublishChannel.Main);
          }

          Loggers.info('Camera state updated via listener: ${isVideoOn ? 'enabled' : 'disabled'}');
        } catch (e) {
          Loggers.error('Error updating camera state via listener: $e');
        }
      }
      if (oldState?.audioStatus != newState.audioStatus) {
        bool isAudioOn =
            newState.audioStatus == VideoAudioStatus.on ? false : true;
        zegoEngine.muteMicrophone(isAudioOn);
      }
      if (oldState?.type == LivestreamUserType.coHost &&
          newState.type == LivestreamUserType.audience) {
        await closeCoHostStream(newState.userId);
      }
    }
  }

  void coHostDelete(LivestreamUserState state) {
    if (state.type == LivestreamUserType.coHost) {
      updateLiveStreamData(coHostId: FieldValue.arrayRemove([state.userId]));
      updateUserStateToFirestore(state.userId,
          type: LivestreamUserType.audience);
    }
  }

  void reportUser(int? userId) {
    Get.bottomSheet(ReportSheet(reportType: ReportType.user, id: userId),
        isScrollControlled: true);
  }

  void _timerStart(VoidCallback callBack) {
    timer = Timer.periodic(
      const Duration(milliseconds: 100),
      (t) {
        callBack.call();
        // if (t.tick >= totalBattleSecond) {
        //   timer?.cancel();
        // }
      },
    );
  }

  void onStopButtonTap() {
    bool isBattleOn = liveData.value.type == LivestreamType.battle;
    String title =
        !isBattleOn ? LKey.endStreamTitle.tr : LKey.stopBattleTitle.tr;
    String description =
        !isBattleOn ? LKey.endStreamMessage.tr : LKey.stopBattleDescription.tr;

    Get.bottomSheet(
        StopLiveStreamSheet(
            onTap: () {
              if (isBattleOn) {
                updateLiveStreamData(
                    battleType: BattleType.initiate,
                    type: LivestreamType.livestream);
                startMinViewerTimeoutCheck();
              } else {
                hostEndStream();
              }
            },
            title: title,
            description: description,
            positiveText: LKey.stop.tr),
        isScrollControlled: true);
  }

  void hostEndStream() {
    streamEnded();
    logoutRoom(2);
  }

  void streamEnded() {
    LivestreamUserState? userState = liveUsersStates
        .firstWhereOrNull((element) => element.userId == myUserId);
    AppUser? user = firestoreController.users
        .firstWhereOrNull((element) => element.userId == myUserId);
    userState?.user = user;
    int viewers = liveUsersStates.length;
    if (isHost) {
      Get.back();
      Get.off(() => LiveStreamEndScreen(
          userState: userState, isHost: isHost, viewers: viewers));
    } else {
      if (userState?.type == LivestreamUserType.coHost) {
        Get.bottomSheet(
                LiveStreamSummary(
                    userState: userState, isHost: isHost, viewers: viewers),
                isScrollControlled: true)
            .then((value) {
          updateUserStateToFirestore(myUserId,
              battleCoin: 0, liveCoin: 0, currentBattleCoin: 0);
          if ((liveData.value.roomID ?? '').isEmpty) {
            Get.back();
          }
        });
      }
    }
  }

  togglePlayerAudioToggle() {
    videoPlayerController.value?.setVolume(isPlayerMute.value ? 1 : 0);
    isPlayerMute.value = !isPlayerMute.value;
  }

  void toggleView() {
    isViewVisible.value = !isViewVisible.value;
  }

  void startBattle() {
    updateLiveStreamData(
      battleType: BattleType.waiting,
      battleDuration: AppRes.battleDurationInMinutes,
      battleCreatedAt: DateTime.now().millisecondsSinceEpoch,
    );
  }

  void battleRunning() {
    Livestream stream = liveData.value;
    // Battle Start Timer Logic

    final startTime =
        DateTime.fromMillisecondsSinceEpoch(stream.battleCreatedAt ?? 0);
    final endTime = startTime
        .add(Duration(seconds: totalBattleSecond + AppRes.battleStartInSecond));

    Loggers.success('Battle Timer Started');

    _timerStart(() {
      final remaining = endTime.difference(DateTime.now()).inSeconds;
      remainingBattleSeconds.value = remaining.clamp(0, totalBattleSecond);

      if (remainingBattleSeconds.value <= 10) {
        if (!countdownPlayer.playing) {
          countdownPlayer
              .seek(Duration(seconds: 10 - remainingBattleSeconds.value));
          countdownPlayer.play();
        }
      }

      Loggers.info(
          '[BATTLE RUNNING] Battle end in ${remainingBattleSeconds.value} sec.');

      if (remainingBattleSeconds.value <= 0) {
        winAudioPlayer.seek(const Duration(seconds: 0));
        winAudioPlayer.play();
        timer?.cancel();
        updateLiveStreamData(battleType: BattleType.end);
      }
    });
  }

  void startMinViewerTimeoutCheck() {
    if (minViewerTimeoutTimer?.isActive ?? false) return;
    Loggers.info(
        'Check Min. Viewers Required to continue live $timeoutMinutes Minutes');
    minViewerTimeoutTimer =
        Timer.periodic(Duration(minutes: timeoutMinutes), (_) {
      minViewerTimeoutTimer?.cancel();
      if ((liveData.value.watchingCount ?? 0) <= minViewersThreshold) {
        isMinViewerTimeout.value = true;
        Loggers.info('Close Stream Because of Min. Viewers');
      }
    });
  }

  void onCloseAudienceBtn() {
    HapticManager.shared.light();
    Get.bottomSheet(ConfirmationSheet(
        title: LKey.exitLiveStreamTitle.tr,
        description: LKey.exitLiveStreamDescription.tr,
        onTap: () async {
          adsController.showInterstitialAdIfAvailable();
          if (liveData.value.coHostIds?.contains(myUserId) ?? false) {
            await closeCoHostStream(myUserId);
          }
          logoutRoom(3);
        }));
  }

  void pushNotificationToFollowers(Livestream liveData) {
    AppUser? hostUser = liveData.getHostUser([]);
    NotificationService.instance.pushNotification(
        type: NotificationType.liveStream,
        title: LKey.liveStreamNotificationTitle
            .trParams({'name': hostUser?.username ?? ''}),
        body: LKey.liveStreamNotificationBody.tr,
        deviceType: 1,
        topic: '${liveData.hostId}_ios',
        data: liveData.toJson());
    NotificationService.instance.pushNotification(
        type: NotificationType.liveStream,
        title: LKey.liveStreamNotificationTitle
            .trParams({'name': hostUser?.username ?? ''}),
        body: LKey.liveStreamNotificationBody.tr,
        deviceType: 0,
        topic: '${liveData.hostId}_android',
        data: liveData.toJson());
  }
}

class StreamView {
  String streamId;
  int streamViewId;
  Widget streamView;
  bool isMuted;

  StreamView(this.streamId, this.streamViewId, this.streamView, this.isMuted);
}

import 'dart:async';
import 'dart:convert';

import 'package:csv/csv.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:ratulive/common/controller/base_controller.dart';
import 'package:ratulive/common/extensions/string_extension.dart';
import 'package:ratulive/common/manager/logger.dart';
import 'package:ratulive/common/manager/session_manager.dart';
import 'package:ratulive/common/service/api/common_service.dart';
import 'package:ratulive/common/service/api/user_service.dart';
import 'package:ratulive/common/service/network_helper/network_helper.dart';
import 'package:ratulive/common/widget/no_internet_sheet.dart';
import 'package:ratulive/common/widget/restart_widget.dart';
import 'package:ratulive/languages/dynamic_translations.dart';
import 'package:ratulive/model/general/settings_model.dart';
import 'package:ratulive/screen/auth_screen/login_screen.dart';
import 'package:ratulive/screen/dashboard_screen/dashboard_screen.dart';
import 'package:ratulive/screen/select_language_screen/select_language_screen.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:ratulive/common/service/subscription/subscription_manager.dart';
import 'package:ratulive/utilities/app_res.dart';

class SplashScreenController extends BaseController {
  late StreamSubscription _subscription;
  bool isOnline = true;

  @override
  void onReady() {
    super.onReady();
    // Allow UI to render first
    Future.delayed(const Duration(milliseconds: 100), () {
      _initializeApp();
    });
  }

  Future<void> _initializeApp() async {
    // Initialize all services in parallel
    await Future.wait([
      _initRevenueCat(),
      _initBranchSDK(),
      _initMobileAds(),
      fetchSettings(),
    ]);

    // Listen for network changes after initialization
    _subscription = NetworkHelper().onConnectionChange.listen((status) {
      isOnline = status;
      if (isOnline) {
        Get.back();
      } else {
        Get.to(() => const NoInternetSheet(), transition: Transition.downToUp);
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    _subscription.cancel();
  }

  Future<void> _initMobileAds() async {
    await MobileAds.instance.initialize();
  }

  Future<void> _initRevenueCat() async {
    try {
      await SubscriptionManager.shared.initPlatformState();
    } catch (e, st) {
      Loggers.error('SubscriptionManager init error: $e\n$st');
    }
  }

  Future<void> _initBranchSDK() async {
    try {
      await FlutterBranchSdk.init();
    } catch (e, st) {
      Loggers.error('Branch SDK init error: $e\n$st');
    }
  }

  Future<void> fetchSettings() async {
    NetworkHelper().initialize();
    await Future.delayed(const Duration(milliseconds: 1000));
    bool showNavigate = await CommonService.instance.fetchGlobalSettings();
    if (showNavigate) {
      final translations = Get.find<DynamicTranslations>();
      var setting = SessionManager.instance.getSettings();
      var languages = setting?.languages ?? [];
      List<Language> downloadLanguages =
          languages.where((element) => element.status == 1).toList();
      if (downloadLanguages.isEmpty) {
        showSnackBar(AppRes.languageAdd, second: 5);
        return;
      }

      var downloadedFiles = await downloadAndParseLanguages(downloadLanguages);

      translations.addTranslations(downloadedFiles);

      var defaultLang =
          languages.firstWhereOrNull((element) => element.isDefault == 1);

      if (defaultLang != null) {
        SessionManager.instance.setFallbackLang(defaultLang.code ?? 'en');
      }

      RestartWidget.restartApp(Get.context!);
      if (SessionManager.instance.isLogin()) {
        UserService.instance
            .fetchUserDetails(userId: SessionManager.instance.getUserID())
            .then((value) {
          if (value != null) {
            Get.off(() => DashboardScreen(myUser: value));
          } else {
            Get.off(() => const LoginScreen());
          }
        });
      } else {
        Get.off(() => const SelectLanguageScreen(
            languageNavigationType: LanguageNavigationType.fromStart));
      }
    }
  }

  Future<Map<String, Map<String, String>>> downloadAndParseLanguages(
      List<Language> languages) async {
    const int maxConcurrentDownloads = 3; // Limit concurrent downloads
    final Set<Future<void>> activeDownloads = {}; // Track active downloads
    final languageData = <String, Map<String, String>>{};

    for (var language in languages) {
      if (language.code != null && language.csvFile != null) {
        // Start the download and add it to the active set
        final downloadTask = downloadAndProcessLanguage(language, languageData);
        activeDownloads.add(downloadTask);

        // Limit concurrency
        if (activeDownloads.length >= maxConcurrentDownloads) {
          // Wait for any download to complete
          await Future.any(activeDownloads);

          // Remove completed tasks from the set
          activeDownloads
              .removeWhere((task) => task == Future.any(activeDownloads));
        }
      }
    }

    // Wait for all remaining downloads to complete
    await Future.wait(activeDownloads);

    return languageData;
  }

  Future<void> downloadAndProcessLanguage(
      Language language, Map<String, Map<String, String>> languageData) async {
    try {
      final response =
          await http.get(Uri.parse(language.csvFile?.addBaseURL() ?? ''));
      if (response.statusCode == 200) {
        final csvContent = utf8.decode(response.bodyBytes);
        // Parse the CSV into a map
        final parsedMap = _parseCsvToMap(csvContent);
        languageData[language.code!] = parsedMap;

        Loggers.info('Downloaded and parsed: ${language.code}');
      } else {
        Loggers.error(
            'Failed to download ${language.code}: ${response.statusCode}');
      }
    } catch (e) {
      Loggers.error('Error downloading ${language.code}: $e');
    }
  }

  Map<String, String> _parseCsvToMap(String csvContent) {
    final rows = const CsvToListConverter().convert(csvContent);
    final map = <String, String>{};

    for (var row in rows) {
      if (row.length >= 2) {
        map[row[0].toString()] = row[1].toString();
      }
    }
    return map;
  }
}

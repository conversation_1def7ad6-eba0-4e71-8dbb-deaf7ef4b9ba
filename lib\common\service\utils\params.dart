class Params {
  static const String apikey = 'apikey';
  static const String fullname = 'fullname';
  static const String username = 'username';
  static const String userEmail = 'user_email';
  static const String userMobileNo = 'user_mobile_no';
  static const String profilePhoto = 'profile_photo';
  static const String bio = 'bio';
  static const String country = 'country';
  static const String countryCode = 'countryCode';
  static const String whoCanViewPost = 'who_can_view_post';
  static const String mobileCountryCode = 'mobile_country_code';
  static const String showMyFollowing = 'show_my_following';
  static const String receiveMessage = 'receive_message';
  static const String isVerify = 'is_verify';
  static const String url = 'url';
  static const String title = 'title';
  static const String linkId = 'link_id';
  static const String identity = 'identity';
  static const String deviceToken = 'device_token';
  static const String device = 'device';
  static const String loginMethod = 'login_method';
  static const String userId = 'user_id';
  static const String postId = 'post_id';
  static const String commentId = 'comment_id';
  static const String replyId = 'reply_id';
  static const String authToken = 'AUTHTOKEN';
  static const String canComment = 'can_comment';
  static const String description = 'description';
  static const String video = 'video';
  static const String thumbnail = 'thumbnail';
  static const String mentionedUserIds = 'mentioned_user_ids';
  static const String hashtags = 'hashtags';
  static const String hashtag = 'hashtag';
  static const String placeTitle = 'place_title';
  static const String placeLat = 'place_lat';
  static const String placeLon = 'place_lon';
  static const String lat = 'lat';
  static const String lon = 'lon';
  static const String state = 'state';
  static const String keyword = 'keyword';
  static const String limit = 'limit';
  static const String lastItemId = 'last_item_id';
  static const String categoryId = 'category_id';
  static const String file = 'file';
  static const String filePath = 'filePath';
  static const String textQuery = 'textQuery';
  static const String locationRestriction = 'locationRestriction';
  static const String includedTypes = 'includedTypes';
  static const String circle = 'circle';
  static const String center = 'center';
  static const String latitude = 'latitude';
  static const String longitude = 'longitude';
  static const String radius = 'radius';

  static const String maxResultCount = 'maxResultCount';
  static const String authorization = 'Authorization';
  static const String postImages = 'post_images';
  static const String comment = 'comment';
  static const String reply = 'reply';
  static const String type = 'type';
  static const String types = 'types';
  static const String content = 'content';
  static const String duration = 'duration';
  static const String reason = 'reason';
  static const String savedMusicIds = 'saved_music_ids';
  static const String appLanguage = 'app_language';
  static const String storyId = 'story_id';
  static const String sound = 'sound';
  static const String soundID = 'sound_id';
  static const String artist = 'artist';
  static const String image = 'image';
  static const String coins = 'coins';
  static const String coinPackageId = 'coin_package_id';
  static const String transactionCode = 'transaction_code';
  static const String purchasedAt = 'purchased_at';
  static const String gateway = 'gateway';
  static const String account = 'account';
  static const String notifyPostLike = 'notify_post_like';
  static const String notifyPostComment = 'notify_post_comment';
  static const String notifyFollow = 'notify_follow';
  static const String notifyMention = 'notify_mention';
  static const String notifyGiftReceived = 'notify_gift_received';
  static const String notifyChat = 'notify_chat';
  static const String giftId = 'gift_id';
  static const String musicId = 'music_id';
  static const String appLastUsedAt = 'app_last_used_at';
  static const String region = 'region';
  static const String regionName = 'regionName';
  static const String timezone = 'timezone';
  static const String metadata = 'metadata';
}

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/common/extensions/string_extension.dart';
import 'package:ratulive/common/manager/haptic_manager.dart';
import 'package:ratulive/common/widget/custom_image.dart';
import 'package:ratulive/common/widget/full_name_with_blue_tick.dart';
import 'package:ratulive/common/widget/loader_widget.dart';
import 'package:ratulive/model/livestream/app_user.dart';
import 'package:ratulive/model/livestream/livestream.dart';
import 'package:ratulive/model/livestream/livestream_user_state.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/audience/widget/live_stream_user_info_sheet.dart';
import 'package:ratulive/screen/live_stream/livestream_screen/livestream_screen_controller.dart';
import 'package:ratulive/utilities/asset_res.dart';
import 'package:ratulive/utilities/text_style_custom.dart';
import 'package:ratulive/utilities/theme_res.dart';
import 'package:ratulive/common/manager/logger.dart';

class LivestreamView extends StatelessWidget {
  final RxList<StreamView> streamViews;
  final LivestreamScreenController controller;

  const LivestreamView(
      {super.key, required this.streamViews, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      Livestream stream = controller.liveData.value;
      final hostId = stream.hostId.toString();
      final views =
          List<StreamView>.from(streamViews); // Optional: clone if needed

      final hostIndex = views.indexWhere((v) => v.streamId == hostId);
      if (hostIndex != -1 && hostIndex != 0) {
        final hostView = views.removeAt(hostIndex);
        views.insert(0, hostView);
      }
      int coHostCount = views.length;
      List<AppUser> liveUsers = controller.firestoreController.users;
      List<AppUser> allUsers = stream.getAllUsers(liveUsers);

      Loggers.info('LivestreamView build - Stream: ${stream.roomID}, Views: ${views.length}, Users: ${allUsers.length}');

      if (allUsers.isEmpty) {
        Loggers.warning('No users found in livestream ${stream.roomID}');
        return _buildEmptyView();
      }

      if (views.isEmpty) {
        Loggers.warning('No stream views available for livestream ${stream.roomID}');
        return _buildLoadingView();
      }

      Loggers.info('Displaying ${views.length} stream views for livestream ${stream.roomID}');

      return switch (coHostCount) {
        2 => OneAndTwoUserView(
            controller: controller,
            streamViews: views,
          ),
        3 => ThreeUserView(controller: controller, streamViews: views),
        4 => FourUserView(controller: controller, streamViews: views),
        1 => LiveStreamUserView(
            isNameAndSpeakerVisible: false,
            streamingView: views.first,
            controller: controller,
          ),
        _ => const SizedBox(),
      };
    });
  }

  Widget _buildEmptyView() {
    return Center(
        child: Text(
      'No users in livestream',
      style: TextStyleCustom.unboundedMedium500(color: Colors.white),
    ));
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const LoaderWidget(),
          const SizedBox(height: 16),
          Text(
            'Connecting to stream...',
            style: TextStyleCustom.unboundedMedium500(color: Colors.white),
          ),
        ],
      ),
    );
  }
}

class OneAndTwoUserView extends StatelessWidget {
  final LivestreamScreenController controller;
  final List<StreamView> streamViews;

  const OneAndTwoUserView({
    super.key,
    required this.controller,
    required this.streamViews,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        streamViews.length,
        (index) => Expanded(
          child: LiveStreamUserView(
            isNameAndSpeakerVisible: index != 0,
            controller: controller,
            streamingView: streamViews[index],
          ),
        ),
      ),
    );
  }
}

class ThreeUserView extends StatelessWidget {
  final LivestreamScreenController controller;
  final List<StreamView> streamViews;

  const ThreeUserView({
    super.key,
    required this.controller,
    required this.streamViews,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildMainUserView(streamViews.first),
        _buildSecondaryUsersRow(streamViews.sublist(1)),
      ],
    );
  }

  Widget _buildMainUserView(StreamView user) {
    return Expanded(
      child: LiveStreamUserView(
        isNameAndSpeakerVisible: false,
        controller: controller,
        streamingView: streamViews.first,
      ),
    );
  }

  Widget _buildSecondaryUsersRow(List<StreamView> streamViews) {
    return Expanded(
      child: Row(
        children: [
          for (final streamView in streamViews.take(2))
            Expanded(
              child: LiveStreamUserView(
                controller: controller,
                streamingView: streamView,
              ),
            ),
          if (streamViews.length < 2) ...[
            for (int i = 0; i < 2 - streamViews.length; i++)
              Expanded(child: _buildEmptyUserView()),
          ],
        ],
      ),
    );
  }
}

class FourUserView extends StatelessWidget {
  final LivestreamScreenController controller;
  final List<StreamView> streamViews;

  const FourUserView(
      {super.key, required this.controller, required this.streamViews});

  @override
  Widget build(BuildContext context) {
    if (streamViews.isEmpty) return _buildEmptyView();

    return Column(
      children: [
        _buildTopRow(streamViews.take(2)),
        _buildBottomRow(streamViews.skip(2)),
      ],
    );
  }

  Widget _buildTopRow(Iterable<StreamView> streamViews) {
    return Expanded(
      child: Row(
        children: [
          for (final user in streamViews.take(2))
            Expanded(
              child: LiveStreamUserView(
                  isNameAndSpeakerVisible:
                      streamViews.toList().indexOf(user) != 0,
                  controller: controller,
                  streamingView: user),
            ),
          if (streamViews.length < 2) Expanded(child: _buildEmptyUserView()),
        ],
      ),
    );
  }

  Widget _buildBottomRow(Iterable<StreamView> streamViews) {
    return Expanded(
      child: Row(
        children: [
          for (final user in streamViews.take(2))
            Expanded(
              child: LiveStreamUserView(
                controller: controller,
                streamingView: user,
              ),
            ),
          if (streamViews.length < 2)
            for (int i = 0; i < 2 - streamViews.length; i++)
              Expanded(child: _buildEmptyUserView()),
        ],
      ),
    );
  }
}

class LiveStreamUserView extends StatelessWidget {
  final bool isNameAndSpeakerVisible;
  final AlignmentGeometry? alignment;
  final StreamView? streamingView;
  final LivestreamScreenController controller;

  const LiveStreamUserView({
    super.key,
    this.isNameAndSpeakerVisible = true,
    this.alignment,
    required this.streamingView,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      LivestreamUserState? state = controller.liveUsersStates.firstWhereOrNull(
          (element) =>
              element.userId == int.parse(streamingView?.streamId ?? ''));
      AppUser? liveUser = controller.firestoreController.users.firstWhereOrNull((element) =>
              element.userId == int.parse(streamingView?.streamId ?? ''));

      final streamId = streamingView?.streamId ?? 'unknown';
      final hasStreamView = streamingView != null;
      final videoStatus = state?.videoStatus;

      Loggers.info('LiveStreamUserView build - StreamID: $streamId, HasView: $hasStreamView, VideoStatus: $videoStatus');

      return Stack(
        children: [
          if (streamingView != null) streamingView!.streamView,
          if (state?.videoStatus != VideoAudioStatus.on)
            Stack(
              children: [
                CustomImage(
                    size: Size(Get.width, Get.height),
                    image: liveUser?.profile?.addBaseURL(),
                    fullName: liveUser?.fullname,
                    radius: 0),
                LayoutBuilder(
                  builder: (context, constraints) => ClipRect(
                    child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                        child: Container(
                          width: constraints.maxWidth,
                          height: constraints.maxHeight,
                          color: Colors.black.withValues(alpha: .5),
                        )),
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: LayoutBuilder(builder: (context, constraints) {
                    double width = ((constraints.maxWidth * 50) / 100);
                    return CustomImage(
                        size: Size(width, width),
                        image: liveUser?.profile?.addBaseURL(),
                        fullName: liveUser?.fullname,
                        strokeWidth: 3);
                  }),
                ),
              ],
            ),
          if (state?.audioStatus != VideoAudioStatus.on)
            Align(
                alignment: Alignment.center,
                child: Image.asset(
                  AssetRes.icMicOff,
                  height: 25,
                  width: 25,
                  color: whitePure(context).withValues(alpha: .6),
                )),
          if (isNameAndSpeakerVisible)
            _buildUserInfoOverlay(context,
                streamView: streamingView!,
                state: state.obs,
                liveUser: liveUser,
                isMuteVisible: liveUser?.userId != controller.myUserId)
        ],
      );
    });
  }

  Widget _buildUserInfoOverlay(BuildContext context,
      {required AppUser? liveUser,
      required Rx<LivestreamUserState?> state,
      required StreamView? streamView,
      required bool isMuteVisible}) {
    return Align(
      alignment: alignment ?? AlignmentDirectional.topStart,
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          spacing: 5,
          children: [
            if (alignment != null && isMuteVisible)
              MuteUnMuteButton(
                isMute: (streamView?.isMuted ?? false).obs,
                onTap: () => controller.toggleStreamAudio(liveUser?.userId),
              ),
            FullNameWithBlueTick(
              username: liveUser?.username,
              fontColor: whitePure(context),
              fontSize: 12,
              isVerify: liveUser?.isVerify,
              onTap: () => _showUserActionSheet(liveUser!, state),
            ),
            if (alignment == null && isMuteVisible)
              MuteUnMuteButton(
                isMute: (streamView?.isMuted ?? false).obs,
                onTap: () => controller.toggleStreamAudio(liveUser?.userId),
              ),
          ],
        ),
      ),
    );
  }

  void _showUserActionSheet(AppUser user, Rx<LivestreamUserState?> state) {
    Get.bottomSheet(
      LiveStreamUserInfoSheet(
          isAudience: !controller.isHost,
          liveUser: user,
          controller: controller),
      isScrollControlled: true,
    );
  }
}

class MuteUnMuteButton extends StatelessWidget {
  final RxBool isMute;
  final VoidCallback? onTap;

  const MuteUnMuteButton({super.key, required this.isMute, this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        HapticManager.shared.light();
        onTap?.call();
      },
      child: Obx(
        () => Image.asset(
          isMute.value ? AssetRes.icSpeakerMute : AssetRes.icSpeaker,
          width: 24,
          height: 24,
          color: whitePure(context).withValues(alpha: .5),
        ),
      ),
    );
  }
}

// Helper extensions for common widgets
extension on Widget {
  Widget _buildEmptyUserView() {
    return Container(
      color: Colors.grey[800],
      child: const Center(child: Icon(Icons.person_off, color: Colors.white54)),
    );
  }

  Widget _buildEmptyView() {
    return const Center(child: Text('No users in livestream'));
  }
}

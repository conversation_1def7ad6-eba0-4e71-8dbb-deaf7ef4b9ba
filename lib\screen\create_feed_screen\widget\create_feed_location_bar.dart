import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ratulive/model/general/location_place_model.dart';
import 'package:ratulive/screen/create_feed_screen/create_feed_screen_controller.dart';
import 'package:ratulive/utilities/asset_res.dart';
import 'package:ratulive/utilities/text_style_custom.dart';
import 'package:ratulive/utilities/theme_res.dart';

class CreateFeedLocationBar extends StatelessWidget {
  final CreateFeedScreenController controller;

  const CreateFeedLocationBar({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      Places? place = controller.selectedLocation.value;

      if (place == null) {
        return const SizedBox();
      }

      return Container(
        height: 47,
        padding: const EdgeInsets.symmetric(horizontal: 15),
        margin: const EdgeInsets.only(top: 5),
        color: bgLight<PERSON>rey(context),
        child: Row(
          children: [
            Image.asset(AssetRes.icLocation, height: 17, width: 17),
            const SizedBox(width: 5),
            Expanded(
                child: Text(place.placeTitle,
                    style: TextStyleCustom.outFitLight300(color: textDark<PERSON>rey(context)),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis)),
            const SizedBox(width: 5),
            InkWell(
              onTap: () {
                controller.selectedLocation.value = null;
              },
              child: Image.asset(AssetRes.icClose,
                  height: 17, width: 17, color: textLightGrey(context)),
            )
          ],
        ),
      );
    });
  }
}

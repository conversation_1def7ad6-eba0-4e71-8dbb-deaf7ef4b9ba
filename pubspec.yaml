name: ratulive
description: "A new Flutter project for Ratulive."

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.4

dependency_overrides:
  super_native_extensions:
    git:
      url: https://github.com/superlistapp/super_native_extensions.git
      ref: refs/pull/525/head
      path: super_native_extensions
  irondash_engine_context:
    git:
      url: https://github.com/irondash/irondash.git
      ref: refs/pull/66/head
      path: engine_context/dart

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8

  ### 🔥 Firebase
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  firebase_messaging: ^15.2.7
  google_mobile_ads: ^6.0.0

  ### 📦 State Management & Storage
  get: ^4.7.2
  get_storage: ^2.1.1

  ### 🎨 UI & Design
  figma_squircle_updated: ^1.0.1
  detectable_text_field: ^3.0.2
  dotted_border: ^3.0.1
  shimmer: ^3.0.0
  google_fonts: ^6.2.1
  expandable_page_view: ^1.0.17
  super_context_menu: ^0.9.1
  readmore: ^3.0.0
  proste_indexed_stack: ^0.2.4
  keyboard_avoider: ^0.2.0
  flutter_staggered_grid_view: ^0.7.0
  smooth_highlight: ^0.1.2
  photo_view: ^0.15.0
  dismissible_page: ^1.0.2

  ### 📸 Media (Image, Video, Audio)
  cached_network_image: ^3.4.1
  audio_waveforms: ^1.3.0
  just_audio: ^0.10.4
  video_player: ^2.10.0
  video_compress: ^3.1.4
  flutter_image_compress: ^2.4.0
  deepar_flutter_plus: ^0.2.0
  flutter_native_video_trimmer: ^1.1.9

  ### 🧭 Location & Maps
  google_maps_flutter: ^2.6.1
  geocoding: ^4.0.0
  geolocator: ^14.0.1

  ### 📷 QR & Barcode
  pretty_qr_code: ^3.4.0
  qr_code_scanner_plus: ^2.0.10+1
  mobile_scanner: ^7.0.1
  google_mlkit_barcode_scanning: ^0.14.1

  ### 📤 File Handling & Pickers
  image_picker: ^1.1.2
  flutter_cache_manager: ^3.4.1
  path_provider: ^2.1.5
  path: ^1.9.1
  gal: ^2.3.1
  mime: ^2.0.0
  csv: ^6.0.0

  ### 🔗 Links & External Services
  url_launcher: ^6.3.1
  flutter_branch_sdk: ^8.5.0
  webview_flutter_plus: ^0.4.19
  http: ^1.4.0
  share_plus: ^11.0.0
  internet_connection_checker_plus: ^2.7.2
  connectivity_plus: ^6.1.4
  collection: ^1.19.1
  html: ^0.15.6

  ### 🔐 Login & Auth
  google_sign_in: ^6.3.0

  ### 🔔 Notifications
  flutter_local_notifications: ^19.3.0

  ### 💳 Purchases & Payments
  purchases_flutter: ^8.10.3

  ### 🧰 Utilities
  intl: ^0.20.2
  uuid: ^4.5.1
  flutter_keyboard_visibility: ^6.0.0
  visibility_detector: ^0.4.0+2
  flutter_widget_from_html_core: ^0.16.0
  wakelock_plus: ^1.3.2
  permission_handler: ^11.4.0
  zego_express_engine: ^3.21.0
  rxdart: ^0.28.0
  image: ^4.5.4

  ### 🔌 Custom Plugins
  retrytech_plugin:
    git:
      url: https://gitlab.com/jeelkhokhariya59/retrytech_plugin.git
      ref: master

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_native_splash: ^2.4.6
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/csv/
    - assets/audios/
    - android/app/src/main/res/mipmap-hdpi/
    - ios/Runner/Assets.xcassets/AppIcon.appiconset/

  fonts:
    - family: Black
      fonts:
        - asset: assets/fonts/Outfit-Black.ttf
          weight: 900
    - family: Bold
      fonts:
        - asset: assets/fonts/Outfit-Bold.ttf
          weight: 700
    - family: ExtraBold
      fonts:
        - asset: assets/fonts/Outfit-ExtraBold.ttf
          weight: 800
    - family: ExtraLight
      fonts:
        - asset: assets/fonts/Outfit-ExtraLight.ttf
          weight: 200
    - family: Light
      fonts:
        - asset: assets/fonts/Outfit-Light.ttf
          weight: 300
    - family: Medium
      fonts:
        - asset: assets/fonts/Outfit-Medium.ttf
          weight: 500
    - family: Regular
      fonts:
        - asset: assets/fonts/Outfit-Regular.ttf
          weight: 400
    - family: SemiBold
      fonts:
        - asset: assets/fonts/Outfit-SemiBold.ttf
          weight: 600
    - family: Thin
      fonts:
        - asset: assets/fonts/Outfit-Thin.ttf
          weight: 100
    - family: Unbounded-Black
      fonts:
        - asset: assets/fonts/Unbounded-Black.ttf
          weight: 900
    - family: Unbounded-Bold
      fonts:
        - asset: assets/fonts/Unbounded-Bold.ttf
          weight: 700
    - family: Unbounded-ExtraBold
      fonts:
        - asset: assets/fonts/Unbounded-ExtraBold.ttf
          weight: 800
    - family: Unbounded-ExtraLight
      fonts:
        - asset: assets/fonts/Unbounded-ExtraLight.ttf
          weight: 200
    - family: Unbounded-Light
      fonts:
        - asset: assets/fonts/Unbounded-Light.ttf
          weight: 300
    - family: Unbounded-Medium
      fonts:
        - asset: assets/fonts/Unbounded-Medium.ttf
          weight: 500
    - family: Unbounded-Regular
      fonts:
        - asset: assets/fonts/Unbounded-Regular.ttf
          weight: 400
    - family: Unbounded-SemiBold
      fonts:
        - asset: assets/fonts/Unbounded-SemiBold.ttf
          weight: 600

flutter_native_splash:
  color: "#000000"
  image: assets/images/logo-home2.png
  android: true
  ios: true

flutter_launcher_icons:
  android: true
  ios: false
  image_path: "assets/images/logo-home2.png"
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/logo-home2.png"

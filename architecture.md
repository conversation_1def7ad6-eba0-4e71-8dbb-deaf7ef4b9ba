# Ratulive App - Complete Architecture Design Document

## Table of Contents
1. [Application Overview](#1-application-overview)
2. [Core Architecture Pattern](#2-core-architecture-pattern)
3. [Project Structure](#3-project-structure)
4. [Key Architectural Components](#4-key-architectural-components)
5. [Core Features & Implementation](#5-core-features--implementation)
6. [Data Flow Architecture](#6-data-flow-architecture)
7. [Third-Party Integrations](#7-third-party-integrations)
8. [Performance Optimizations](#8-performance-optimizations)
9. [Security Implementation](#9-security-implementation)
10. [Localization & Internationalization](#10-localization--internationalization)
11. [Testing & Quality Assurance](#11-testing--quality-assurance)
12. [Deployment Architecture](#12-deployment-architecture)
13. [Scalability Considerations](#13-scalability-considerations)

---

## 1. Application Overview

Ratulive is a comprehensive social media platform built with Flutter that combines features from TikTok, Instagram, and live streaming platforms. It's a feature-rich application supporting posts, reels, stories, live streaming, chat, and social interactions.

### Key Features
- **Social Media Posts**: Text, image, video content sharing
- **Short-form Videos (Reels)**: TikTok-like vertical video feed
- **Stories**: Temporary content sharing (24-hour expiry)
- **Live Streaming**: Real-time broadcasting with audience interaction
- **Chat System**: Direct messaging with multimedia support
- **Social Interactions**: Follow, like, comment, share functionality
- **Content Discovery**: Explore, search, and hashtag-based discovery
- **Monetization**: Virtual gifts, coins, and in-app purchases

---

## 2. Core Architecture Pattern

### Architecture Pattern
- **Pattern**: MVC (Model-View-Controller) with GetX state management
- **State Management**: GetX (Reactive programming)
- **Navigation**: GetX Navigation
- **Dependency Injection**: GetX Dependency Injection
- **Storage**: GetStorage for local persistence

### Technology Stack
- **Frontend**: Flutter (Dart)
- **State Management**: GetX
- **Backend Communication**: REST API + Firebase
- **Real-time Features**: Firebase Firestore + ZEGO SDK
- **Local Storage**: GetStorage
- **Media Processing**: Native plugins

---

## 3. Project Structure

### Root Directory Structure
```
ratulive/
├── lib/                    # Main application code
├── android/               # Android-specific configuration
├── ios/                   # iOS-specific configuration
├── assets/                # Static assets (images, fonts, etc.)
├── test/                  # Unit and widget tests
├── pubspec.yaml          # Dependencies and project configuration
└── README.md             # Project documentation
```

### lib/ Directory Structure

#### **lib/common/** - Shared Components
```
common/
├── controller/          # Base controllers and shared business logic
│   ├── base_controller.dart         # Base class for all controllers
│   ├── firebase_firestore_controller.dart  # Firestore operations
│   ├── ads_controller.dart          # Advertisement management
│   ├── follow_controller.dart       # Follow/unfollow logic
│   └── profile_controller.dart      # Profile-related operations
├── enum/               # Application enumerations
│   └── chat_enum.dart              # Chat-related enums
├── extensions/         # Dart extensions for common operations
│   ├── common_extension.dart       # General extensions
│   ├── datetime_extension.dart     # Date/time utilities
│   ├── string_extension.dart       # String manipulation
│   └── user_extension.dart         # User model extensions
├── functions/          # Utility functions
│   ├── debounce_action.dart        # Debouncing utility
│   ├── generate_color.dart         # Color generation
│   └── media_picker_helper.dart    # Media selection helper
├── manager/           # Service managers
│   ├── session_manager.dart        # User session and storage
│   ├── firebase_notification_manager.dart  # Push notifications
│   ├── ads_manager.dart            # Advertisement management
│   ├── branch_io_manager.dart      # Deep linking
│   ├── internet_connection_manager.dart  # Network monitoring
│   ├── haptic_manager.dart         # Haptic feedback
│   └── timer_manager.dart          # Timer utilities
├── service/           # Business logic services
│   ├── api/           # API communication layer
│   │   ├── api_service.dart        # Core API service
│   │   ├── user_service.dart       # User-related APIs
│   │   ├── post_service.dart       # Post-related APIs
│   │   ├── common_service.dart     # Common APIs
│   │   └── notification_service.dart # Notification APIs
│   ├── location/      # Location services
│   ├── navigation/    # Navigation utilities
│   ├── network_helper/ # Network connectivity
│   ├── subscription/  # In-app purchase services
│   ├── url_extractor/ # URL metadata extraction
│   ├── utils/         # Service utilities
│   └── video_cache_helper/ # Video caching
└── widget/           # Reusable UI components
    ├── custom_app_bar.dart         # Custom app bar
    ├── custom_image.dart           # Image widget
    ├── loader_widget.dart          # Loading indicators
    ├── text_field_custom.dart      # Custom text fields
    └── [50+ custom widgets]        # Extensive UI components
```

#### **lib/screen/** - Feature Modules
Each screen follows the pattern: `screen_name/screen_name.dart` + `screen_name_controller.dart`

**Major Feature Modules:**
```
screen/
├── auth_screen/           # Authentication (login, register)
├── dashboard_screen/      # Main navigation hub
├── home_screen/          # Reels feed (discover/following/nearby)
├── feed_screen/          # Posts feed (images, videos, text)
├── live_stream/          # Live streaming functionality
│   ├── livestream_screen/    # Main streaming interface
│   ├── create_live_stream_screen/ # Stream creation
│   └── live_stream_end_screen/   # Stream conclusion
├── explore_screen/       # Content discovery
├── message_screen/       # Chat system
├── profile_screen/       # User profiles
├── camera_screen/        # Content creation
├── story_view_screen/    # Stories viewer
├── reels_screen/         # Reels player
├── post_screen/          # Post viewer
├── comment_sheet/        # Comments interface
├── settings_screen/      # App settings
└── [30+ other screens]   # Additional features
```

#### **lib/model/** - Data Models
```
model/
├── user_model/        # User-related data structures
│   ├── user_model.dart         # Main user model
│   ├── follower_model.dart     # Follower data
│   ├── following_model.dart    # Following data
│   └── block_user_model.dart   # Blocked users
├── post_story/        # Posts, stories, comments, music
│   ├── post_model.dart         # Post data structure
│   ├── comment/               # Comment models
│   ├── story/                 # Story models
│   └── music/                 # Music models
├── livestream/        # Live streaming data models
│   ├── livestream.dart         # Stream data
│   ├── livestream_comment.dart # Stream comments
│   └── app_user.dart          # Stream user state
├── chat/             # Chat and messaging models
│   ├── chat_thread.dart        # Conversation threads
│   └── message_data.dart       # Message structure
├── general/          # Common data structures
│   ├── settings_model.dart     # App settings
│   ├── countries_model.dart    # Country data
│   └── location_place_model.dart # Location data
└── misc/             # Miscellaneous models
    ├── activity_notification_model.dart
    └── admin_notification_model.dart
```

#### **lib/utilities/** - Configuration & Resources
```
utilities/
├── const_res.dart      # API endpoints and constants
├── app_res.dart        # Application resources and limits
├── theme_res.dart      # UI themes and styling
├── color_res.dart      # Color definitions
├── asset_res.dart      # Asset paths
├── font_res.dart       # Font definitions
├── style_res.dart      # Style utilities
└── firebase_const.dart # Firebase collection names
```

#### **lib/languages/** - Internationalization
```
languages/
├── dynamic_translations.dart   # Translation management
└── languages_keys.dart        # Translation keys
```

---

## 4. Key Architectural Components

### **State Management (GetX)**
```dart
// Reactive variables
RxList<Post> reels = <Post>[].obs;
RxBool isLoading = false.obs;
RxInt selectedIndex = 0.obs;

// Controllers extend BaseController
class HomeScreenController extends BaseController {
  // Reactive state
  Rx<TabType> selectedReelCategory = TabType.values.first.obs;
  
  // Business logic methods
  Future<void> onRefreshPage() async { /* Implementation */ }
  void onTabTypeChanged(TabType tabType) { /* Implementation */ }
}
```

### **Base Controller Pattern**
```dart
class BaseController extends FullLifeCycleController {
  RxBool isLoading = false.obs;
  static final share = BaseController();
  
  // Common UI operations
  void showLoader({bool barrierDismissible = true}) { /* Implementation */ }
  void stopLoader() { /* Implementation */ }
  void showSnackBar(String? title, {int second = 2}) { /* Implementation */ }
}
```

### **Service Layer Architecture**
```dart
class ApiService {
  static final ApiService instance = ApiService._();
  
  // Generic API call method with error handling
  Future<T> call<T>({
    required String url,
    Map<String, dynamic>? param,
    CancelToken? cancelToken,
    T Function(Map<String, dynamic> json)? fromJson,
    Function()? onError,
  }) async {
    // Implementation with comprehensive error handling
  }
  
  // Multipart file upload with progress tracking
  Future<T> multiPartCallApi<T>({
    required String url,
    Map<String, dynamic>? param,
    required Map<String, List<XFile?>> filesMap,
    Function(double percentage)? onProgress,
  }) async {
    // Implementation
  }
}
```

### **Session Management**
```dart
class SessionManager {
  static var instance = SessionManager();
  var storage = GetStorage('ratulive');
  
  // User session methods
  void setUser(User? user) { /* Implementation */ }
  User? getUser() { /* Implementation */ }
  void setAuthToken(Token? token) { /* Implementation */ }
  String getAuthToken() { /* Implementation */ }
  
  // Settings and preferences
  void setLang(String langCode) { /* Implementation */ }
  String getLang() { /* Implementation */ }
  void setSettings(Setting settings) { /* Implementation */ }
  Setting? getSettings() { /* Implementation */ }
}
```

---

## 5. Core Features & Implementation

### **Authentication System**
- **Login Methods**: Email, Google, Apple Sign-In
- **Session Management**: Persistent login with GetStorage
- **Token Management**: JWT tokens with automatic refresh
- **Security**: Encrypted local storage, secure API communication

**Implementation:**
```dart
enum LoginMethod {
  email, google, apple;
  
  String title() {
    switch (this) {
      case LoginMethod.email: return 'email';
      case LoginMethod.google: return 'google';
      case LoginMethod.apple: return 'apple';
    }
  }
}

class UserService {
  Future<User?> logInUser({
    String? fullName,
    required String identity,
    String? deviceToken,
    required LoginMethod loginMethod,
  }) async {
    // API call implementation
  }
}
```

### **Content Management**
- **Post Types**: Text, Image, Video, Reel (enum-based type system)
- **Media Processing**: Compression, filtering, trimming
- **Content Moderation**: SightEngine integration for automated moderation
- **Storage**: Firebase Storage for media files

**Post Type System:**
```dart
enum PostType {
  reel, image, video, text, none;
  
  int get type {
    switch (this) {
      case PostType.reel: return 1;
      case PostType.image: return 2;
      case PostType.video: return 3;
      case PostType.text: return 4;
      case PostType.none: return 0;
    }
  }
}
```

### **Live Streaming Architecture**
- **SDK**: ZEGO Express Engine for WebRTC communication
- **Real-time Communication**: Peer-to-peer video/audio streaming
- **Features**: Host/audience modes, co-hosting, battles, virtual gifts
- **State Management**: Firebase Firestore for real-time stream state

**Live Stream Controller Structure:**
```dart
class LivestreamScreenController extends BaseController {
  ZegoExpressEngine zegoEngine = ZegoExpressEngine.instance;
  FirebaseFirestore db = FirebaseFirestore.instance;
  
  // Stream state management
  RxList<StreamView> streamViews = <StreamView>[].obs;
  RxList<LivestreamComment> comments = <LivestreamComment>[].obs;
  RxList<LivestreamUserState> liveUsersStates = <LivestreamUserState>[].obs;
  
  // Core streaming methods
  Future<ZegoRoomLoginResult> loginRoom() async { /* Implementation */ }
  Future<void> startHostPublish() async { /* Implementation */ }
  Future<void> startPlayStream(String streamID) async { /* Implementation */ }
}
```

### **Chat System**
- **Backend**: Firebase Firestore for real-time messaging
- **Features**: Text messages, media sharing, post sharing, story replies
- **Real-time Updates**: Stream subscriptions for live message updates
- **Message Types**: Text, image, video, audio, post, story reply

**Chat Architecture:**
```dart
class ChatScreenController extends BaseController {
  StreamSubscription<QuerySnapshot<MessageData>>? messageListener;
  RxList<MessageData> messages = <MessageData>[].obs;
  
  void listenMessages() {
    messageListener = FirebaseFirestore.instance
        .collection(FirebaseConst.chatRooms)
        .doc(conversationUser.conversationId)
        .collection(FirebaseConst.messages)
        .snapshots()
        .listen((snapshot) {
          // Real-time message handling
        });
  }
}
```

### **Notification System**
- **Push Notifications**: Firebase Cloud Messaging
- **Local Notifications**: Flutter Local Notifications
- **Types**: Chat, post interactions, live streams, follows, mentions
- **Localization**: Multi-language notification support

---

## 6. Data Flow Architecture

### **API Communication Flow**
```
UI Layer (Screens) 
    ↓ User Interaction
Controller Layer (Business Logic)
    ↓ Data Processing
Service Layer (API Services)
    ↓ HTTP Requests
Network Layer (HTTP Client)
    ↓ API Calls
Backend API
    ↓ Response
Data Models (JSON Parsing)
    ↓ State Update
UI Layer (Reactive Updates)
```

### **State Management Flow**
```
User Action → Controller Method → Update Observable → UI Rebuilds Automatically
```

### **Real-time Data Flow (Firebase)**
```
Firebase Firestore → Stream Subscription → Controller → Observable Update → UI Update
```

### **Video Streaming Flow**
```
Camera Input → ZEGO SDK → WebRTC → Network → Audience Devices → Video Player
```

---

## 7. Third-Party Integrations

### **Firebase Services**
- **Authentication**: User management and social login
- **Firestore**: Real-time database for chat, live streams, user states
- **Cloud Messaging**: Push notifications
- **Storage**: File uploads and media storage
- **Analytics**: User behavior tracking

### **Media & Communication**
- **ZEGO Express Engine**: Live streaming and video calls
- **Just Audio**: Audio playback for music and sound effects
- **Video Player**: Video content playback with controls
- **Image Picker**: Camera and gallery media selection
- **Audio Waveforms**: Audio visualization
- **Video Compress**: Video compression for uploads

### **Social Features**
- **Branch.io**: Deep linking and content sharing
- **Google Maps**: Location services and place search
- **RevenueCat**: In-app purchases and subscription management
- **Google Mobile Ads**: Advertisement integration
- **Share Plus**: Native sharing functionality

### **UI/UX Libraries**
- **Cached Network Image**: Efficient image loading and caching
- **Shimmer**: Loading animations and skeleton screens
- **Photo View**: Zoomable image viewing
- **Dismissible Page**: Swipe-to-dismiss functionality
- **Smooth Highlight**: Text highlighting effects
- **Custom Widgets**: 50+ custom UI components

### **Utility Libraries**
- **Permission Handler**: Runtime permissions management
- **Connectivity Plus**: Network connectivity monitoring
- **Geolocator**: GPS location services
- **URL Launcher**: External URL handling
- **Path Provider**: File system access
- **Wakelock Plus**: Screen wake lock for live streams

---

## 8. Performance Optimizations

### **Memory Management**
- **Video Controller Disposal**: Proper cleanup of video players to prevent memory leaks
- **Image Caching**: Multi-level caching strategy with automatic cleanup
- **Stream Subscriptions**: Proper cancellation in controller disposal
- **Widget Lifecycle**: Efficient widget creation and destruction

**Video Memory Management:**
```dart
class ReelsScreenController extends BaseController {
  RxMap<int, VideoPlayerController> videoControllers = <int, VideoPlayerController>{}.obs;
  
  void _disposeControllerAtIndex(int index) {
    final VideoPlayerController? controller = videoControllers[index];
    if (controller != null) {
      controller.dispose();
      videoControllers.remove(index);
    }
  }
  
  @override
  void onClose() {
    disposeAllController();
    super.onClose();
  }
}
```

### **Network Optimization**
- **Request Cancellation**: CancelToken implementation for API calls
- **Pagination**: Efficient data loading with configurable limits
- **Retry Logic**: Automatic retry for failed requests with exponential backoff
- **Caching Strategy**: Multi-level caching (memory, disk, network)

**Network Optimization Example:**
```dart
class PostService {
  Future<List<Post>> fetchPostsDiscover({
    required String type, 
    CancelToken? cancelToken
  }) async {
    PostsModel model = await ApiService.instance.call(
      url: WebService.post.fetchPostsDiscover,
      param: {Params.limit: AppRes.paginationLimit, Params.types: type},
      fromJson: PostsModel.fromJson,
      cancelToken: cancelToken
    );
    return model.data ?? [];
  }
}
```

### **UI Performance**
- **Lazy Loading**: On-demand widget creation and data loading
- **Indexed Stack**: Efficient tab switching without rebuilding
- **Custom Scroll Physics**: Smooth scrolling experience
- **Widget Recycling**: Efficient list rendering with proper itemBuilder

**Efficient Tab Management:**
```dart
ProsteIndexedStack(
  index: controller.selectedPageIndex.value,
  children: [
    IndexedStackChild(child: const HomeScreen(), preload: true),
    IndexedStackChild(child: FeedScreen(myUser: myUser), preload: true),
    // Other tabs with selective preloading
  ],
)
```

### **Video Streaming Optimization**
- **Video Caching**: Local caching of frequently accessed videos
- **Preloading**: Smart preloading of next/previous videos
- **Quality Adaptation**: Automatic quality adjustment based on network
- **Buffer Management**: Efficient video buffer management

---

## 9. Security Implementation

### **API Security**
- **Authentication Headers**: JWT token validation on all requests
- **Request Signing**: API key validation for request authenticity
- **SSL/TLS**: Encrypted HTTPS communication
- **Rate Limiting**: Protection against API abuse

**API Security Implementation:**
```dart
class ApiService {
  var header = {Params.apikey: apiKey};
  
  Future<T> call<T>({required String url, Map<String, dynamic>? param}) async {
    if (!cancelAuthToken) {
      header[Params.authToken] = SessionManager.instance.getAuthToken();
    }
    
    final response = await client.post(Uri.parse(url), headers: header, body: params);
    
    if (response.statusCode == 401) {
      // Handle unauthorized access
      Get.offAll(() => const SessionExpiredScreen());
    }
  }
}
```

### **Data Protection**
- **Local Storage Encryption**: Sensitive data protection with GetStorage
- **Content Moderation**: Automated content filtering with SightEngine
- **Privacy Controls**: Granular user privacy settings
- **Data Sanitization**: Input validation and sanitization

### **User Privacy**
- **Permission Management**: Runtime permission requests
- **Data Minimization**: Collect only necessary user data
- **GDPR Compliance**: User data rights and deletion
- **Privacy Settings**: User-controlled privacy options

---

## 10. Localization & Internationalization

### **Dynamic Translations**
- **CSV-based**: Server-side translation management
- **Runtime Loading**: Dynamic language switching without app restart
- **Fallback System**: Default language support for missing translations
- **Parameterized Translations**: Dynamic content insertion

**Translation System:**
```dart
class DynamicTranslations extends Translations {
  final Map<String, Map<String, String>> _keys = {};
  
  @override
  Map<String, Map<String, String>> get keys => _keys;
  
  void addTranslations(Map<String, Map<String, String>> map) {
    map.forEach((lang, translations) {
      if (_keys.containsKey(lang)) {
        _keys[lang]?.addAll(translations);
      } else {
        _keys[lang] = translations;
      }
    });
  }
}
```

### **Language Management**
- **Server-side Control**: Admin panel language management
- **Automatic Detection**: Device language detection
- **User Preference**: Manual language selection
- **RTL Support**: Right-to-left language support

---

## 11. Testing & Quality Assurance

### **Error Handling**
- **Global Error Handling**: Centralized error management system
- **Network Error Recovery**: Automatic retry mechanisms
- **User-Friendly Messages**: Localized error messages
- **Graceful Degradation**: App functionality during errors

**Error Handling Implementation:**
```dart
class ApiService {
  Future<T> call<T>({required String url}) async {
    try {
      final response = await client.post(Uri.parse(url));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as T;
      } else {
        throw Exception("HTTP Error: ${response.statusCode}");
      }
    } on HttpException {
      throw Exception('Could not connect to the server');
    } on FormatException catch (e) {
      throw Exception("Invalid JSON format: ${e.message}");
    } catch (e) {
      rethrow;
    }
  }
}
```

### **Logging System**
- **Debug Logging**: Development debugging with detailed logs
- **Error Tracking**: Production error monitoring
- **Performance Monitoring**: App performance metrics
- **User Analytics**: User behavior tracking

**Logging Implementation:**
```dart
class Loggers {
  static void info(String message) { /* Implementation */ }
  static void success(String message) { /* Implementation */ }
  static void error(String message) { /* Implementation */ }
  static void warning(String message) { /* Implementation */ }
}
```

### **Quality Assurance**
- **Code Standards**: Consistent coding standards and linting
- **Performance Testing**: Regular performance benchmarking
- **Security Audits**: Regular security assessments
- **User Testing**: Beta testing and feedback integration

---

## 12. Deployment Architecture

### **Build Configuration**
- **Environment-specific**: Development, staging, production configurations
- **Feature Flags**: Conditional feature enabling
- **Version Management**: Automated versioning and build numbering
- **CI/CD Pipeline**: Automated testing and deployment

**Environment Configuration:**
```dart
// const_res.dart
const String baseURL = 'https://ratushortz.alviongs.com/';
const String apiURL = '${baseURL}api/';
const String apiKey = 'retry123';

// Different configurations for different environments
String revenueCatAndroidApiKey = "your_revenueCat_android_api_key";
String revenueCatAppleApiKey = "your_revenueCat_iOS_api_key";
String branchKey = "key_live_avwixet0Lk8mwMkgObX83jofxrgINDPe";
```

### **Platform-specific Features**
- **Android**: Deep linking, notifications, permissions, background processing
- **iOS**: App Store compliance, background app refresh, privacy permissions
- **Cross-platform**: Shared business logic with platform-specific implementations

**Android Configuration:**
```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

<!-- Deep linking configuration -->
<intent-filter>
    <data android:host="open" android:scheme="ratulive" />
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
</intent-filter>
```

### **Release Management**
- **App Store Optimization**: Metadata and asset optimization
- **Beta Testing**: TestFlight (iOS) and Play Console (Android) beta testing
- **Rollout Strategy**: Gradual rollout with monitoring
- **Rollback Plan**: Quick rollback procedures for critical issues

---

## 13. Scalability Considerations

### **Modular Architecture**
- **Feature Modules**: Independent feature development and testing
- **Shared Components**: Reusable UI and business logic components
- **Service Abstraction**: Easy service replacement and upgrades
- **Plugin Architecture**: Extensible functionality through plugins

### **Performance Scaling**
- **Lazy Loading**: On-demand resource loading and initialization
- **Caching Strategy**: Multi-level caching (memory, disk, CDN)
- **Background Processing**: Non-blocking operations and background tasks
- **Resource Optimization**: Efficient resource usage and cleanup

### **Database Scaling**
- **Firebase Firestore**: Automatic scaling with real-time capabilities
- **Data Partitioning**: Efficient data organization and querying
- **Indexing Strategy**: Optimized database queries
- **Caching Layer**: Reduced database load through intelligent caching

### **Infrastructure Scaling**
- **CDN Integration**: Global content delivery for media files
- **Load Balancing**: Distributed server load management
- **Microservices**: Scalable backend architecture
- **Auto-scaling**: Automatic resource scaling based on demand

---

## Conclusion

This architecture demonstrates a well-structured, scalable Flutter application that follows modern development practices and patterns. The Ratulive app successfully integrates multiple complex features including social media functionality, live streaming, real-time chat, and content management while maintaining:

- **Code Organization**: Clear separation of concerns with modular architecture
- **Performance Optimization**: Efficient resource management and caching strategies
- **Scalability**: Designed for growth with modular components and service abstraction
- **Security**: Comprehensive security measures for user data and API communication
- **User Experience**: Smooth, responsive UI with offline capabilities
- **Maintainability**: Clean code structure with proper documentation and error handling

The architecture supports rapid feature development while maintaining code quality and performance standards suitable for a production social media application.
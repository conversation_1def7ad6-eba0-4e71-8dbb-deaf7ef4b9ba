<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>NSUserTrackingUsageDescription</key>
		<string>This identifier will be used to deliver personalized ads to you.</string>
		<key>GADApplicationIdentifier</key>
		<string>ca-app-pub-3940256099942544~1458002511</string>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Ratulive</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Ratulive</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Google Sign in</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.463711886346-1a94m5n1asum7bvf345ekrob1cr2e3a3</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Branch URL Scheme</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ratulive</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>instagram</string>
			<string>whatsapp</string>
			<string>twitter</string>
			<string>tg</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
			<key>NSAllowsArbitraryLoadsForMedia</key>
			<true/>
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>We require access to your camera to allow you to capture photos and videos for creating posts, reels, and stories.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We use your location to show nearby posts and enhance your experience. Your location is only accessed while using the app.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>We require access to your microphone to enable audio recording for creating posts, reels, and stories.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>This app needs permission to save images to your gallery.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>This app requires access to your photo library to allow you to select a profile photo.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
			<string>fetch</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarStyle</key>
		<string></string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>branch_key</key>
		<string>your_branch_key</string>
		<key>branch_universal_link_domains</key>
		<array>
			<string>your_default_link_domain</string>
			<string>your_alternate_link_domain</string>
		</array>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
	</dict>
</plist>

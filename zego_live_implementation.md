# ZEGO Live Streaming Implementation - Complete Technical Documentation

## Table of Contents
1. [Overview](#1-overview)
2. [Architecture Components](#2-architecture-components)
3. [ZEGO SDK Integration](#3-zego-sdk-integration)
4. [Live Stream Creation Flow](#4-live-stream-creation-flow)
5. [Host Implementation](#5-host-implementation)
6. [Audience Implementation](#6-audience-implementation)
7. [Stream Management](#7-stream-management)
8. [Real-time Features](#8-real-time-features)
9. [Battle Mode Implementation](#9-battle-mode-implementation)
10. [State Management](#10-state-management)
11. [Error Handling & Recovery](#11-error-handling--recovery)
12. [Performance Optimizations](#12-performance-optimizations)
13. [Security & Privacy](#13-security--privacy)

---

## 1. Overview

The Ratulive app implements a comprehensive live streaming system using **ZEGO Express Engine SDK** for WebRTC-based real-time communication, combined with **Firebase Firestore** for real-time state management and chat functionality.

### Key Features
- **Real-time Video Streaming**: Host and audience video/audio streaming
- **Interactive Chat**: Real-time messaging during live streams
- **Co-hosting**: Multiple participants can join as co-hosts
- **Battle Mode**: Competitive streaming between two hosts
- **Virtual Gifts**: Monetization through virtual gift system
- **Stream Management**: Join requests, audience management, moderation
- **Multi-platform Support**: iOS and Android with platform-specific optimizations

### Technology Stack
- **Streaming SDK**: ZEGO Express Engine
- **Real-time Database**: Firebase Firestore
- **State Management**: GetX (Reactive programming)
- **Video Processing**: Native platform video players
- **Audio Management**: ZEGO audio engine with custom controls

---

## 2. Architecture Components

### Core Components Structure
```
lib/screen/live_stream/
├── create_live_stream_screen/     # Stream creation and setup
├── livestream_screen/             # Main streaming interface
│   ├── host/                      # Host-specific UI and logic
│   ├── audience/                  # Audience-specific UI and logic
│   ├── view/                      # Shared UI components
│   └── widget/                    # Common widgets
├── live_stream_end_screen/        # Stream conclusion
└── live_stream_search_screen/     # Stream discovery
```

### Key Classes and Controllers

#### **LivestreamScreenController** - Core Controller
```dart
class LivestreamScreenController extends BaseController {
  // ZEGO Engine Integration
  ZegoExpressEngine zegoEngine = ZegoExpressEngine.instance;
  FirebaseFirestore db = FirebaseFirestore.instance;
  
  // Stream State Management
  RxList<StreamView> streamViews = <StreamView>[].obs;
  RxList<LivestreamComment> comments = <LivestreamComment>[].obs;
  RxList<LivestreamUserState> liveUsersStates = <LivestreamUserState>[].obs;
  
  // Core streaming methods
  Future<ZegoRoomLoginResult> loginRoom();
  Future<void> startHostPublish();
  Future<void> startPlayStream(String streamID);
  Future<void> logoutRoom(int index);
}
```

#### **StreamView Model** - Video Stream Representation
```dart
class StreamView {
  String streamId;        // Unique stream identifier
  int streamViewId;       // ZEGO view ID
  Widget streamView;      // Flutter widget for video display
  bool isMuted;          // Audio mute state
}
```

#### **RoomManager** - Singleton Room State Manager
```dart
class RoomManager {
  // Prevents duplicate room logins
  Set<String> _roomsBeingJoined;
  Map<String, String> _roomErrors;
  Map<String, String> _activeRooms;
  
  bool isRoomBeingJoined(String roomID);
  void markRoomAsJoined(String roomID, String controllerTag);
  void cleanupRoom(String roomID);
}
```

---

## 3. ZEGO SDK Integration

### SDK Initialization and Configuration
```dart
class CreateLiveStreamScreenController extends BaseController {
  ZegoExpressEngine zegoEngine = ZegoExpressEngine.instance;
  
  void initZegoEngine() async {
    bool isPermissionGranted = await requestPermission();
    if (isPermissionGranted) {
      await initializeCameraPreview();
    }
  }
  
  Future<void> initializeCameraPreview() async {
    // Enable camera and audio
    await zegoEngine.enableCamera(true);
    await zegoEngine.mutePublishStreamAudio(false);
    zegoEngine.muteMicrophone(false);
    
    // Set camera orientation
    zegoEngine.useFrontCamera(true, channel: ZegoPublishChannel.Main);
    
    // Create preview canvas
    await zegoEngine.createCanvasView((viewID) async {
      ZegoCanvas previewCanvas = ZegoCanvas(viewID, viewMode: ZegoViewMode.AspectFill);
      zegoEngine.startPreview(canvas: previewCanvas);
    });
  }
}
```

### Room Login and Management
```dart
Future<ZegoRoomLoginResult> loginRoom({int retryCount = 0}) async {
  final roomID = liveData.value.roomID ?? '';
  final user = ZegoUser('$myUserId', myUser.value?.username ?? '');
  
  // Room validation
  if (!_isValidRoomID(roomID)) {
    throw Exception('Invalid room ID format');
  }
  
  // Logout from previous room
  await zegoEngine.logoutRoom();
  await Future.delayed(const Duration(milliseconds: 200));
  
  // Login to new room
  final roomConfig = ZegoRoomConfig.defaultConfig()..isUserStatusNotify = true;
  final result = await zegoEngine.loginRoom(roomID, user, config: roomConfig);
  
  if (result.errorCode != 0) {
    // Handle login errors with retry logic
    if (_shouldRetryLogin(result.errorCode) && retryCount < 3) {
      await Future.delayed(const Duration(seconds: 2));
      return loginRoom(retryCount: retryCount + 1);
    }
    throw Exception("Room login failed: ${result.errorCode}");
  }
  
  return result;
}
```

### Stream Event Handling
```dart
void startListenEvent() async {
  // Room user updates
  ZegoExpressEngine.onRoomUserUpdate = (roomID, updateType, List<ZegoUser> userList) {
    if (userList.length > 1) {
      ZegoExpressEngine.instance.setAudioRouteToSpeaker(true);
    }
  };
  
  // Stream updates
  ZegoExpressEngine.onRoomStreamUpdate = (roomID, updateType, List<ZegoStream> streamList, extendedData) async {
    switch (updateType) {
      case ZegoUpdateType.Add:
        for (final stream in streamList) {
          startPlayStream(stream.streamID);
        }
        break;
      case ZegoUpdateType.Delete:
        for (final stream in streamList) {
          streamViews.removeWhere((element) => element.streamId == stream.streamID);
          stopPlayStream(stream.streamID);
        }
        break;
    }
  };
}
```

---

## 4. Live Stream Creation Flow

### Stream Setup Process
```dart
Future<void> onStartLive() async {
  // Validation checks
  if ((myUser.value?.followerCount ?? 0) < (_setting?.minFollowersForLive ?? 0)) {
    showSnackBar("Minimum followers required");
    return;
  }
  
  if (titleController.text.trim().isEmpty) {
    return showSnackBar("Enter live stream title");
  }
  
  // Create stream models
  int time = DateTime.now().millisecondsSinceEpoch;
  Livestream livestream = user.livestream(
    type: LivestreamType.livestream,
    time: time,
    description: titleController.text.trim(),
    restrictToJoin: isRestricted.value ? 1 : 0,
    hostViewId: localViewID.value
  );
  
  // Firebase batch operation
  WriteBatch batch = db.batch();
  batch.set(livestreamRef, livestream.toJson());
  batch.set(usersRef, livestreamUser.toJson());
  batch.set(userStateRef, livestreamUserState.toJson());
  await batch.commit();
  
  // Navigate to host screen
  Get.off(() => LivestreamHostScreen(
    hostPreview: localView.value,
    livestream: livestream,
    isHost: true
  ));
}
```

### Camera Preview Setup
```dart
Future<void> initializeCameraPreview() async {
  try {
    showLoader();
    
    // Configure camera and audio
    await zegoEngine.enableCamera(true);
    await zegoEngine.mutePublishStreamAudio(false);
    zegoEngine.muteMicrophone(false);
    zegoEngine.useFrontCamera(true, channel: ZegoPublishChannel.Main);
    
    // Create canvas view
    await zegoEngine.createCanvasView((viewID) async {
      localViewID.value = viewID;
      ZegoCanvas previewCanvas = ZegoCanvas(viewID, viewMode: ZegoViewMode.AspectFill);
      zegoEngine.startPreview(canvas: previewCanvas);
    }).then((canvasViewWidget) {
      localView.value = canvasViewWidget;
    });
  } catch (e) {
    Loggers.error('Failed to initialize camera preview: $e');
  } finally {
    stopLoader();
  }
}
```

---

## 5. Host Implementation

### Host Screen Architecture
```dart
class LivestreamHostScreen extends StatelessWidget {
  final Livestream livestream;
  final Widget? hostPreview;
  final bool isHost;
  
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LivestreamScreenController(
      livestream.obs, 
      isHost,
      hostPreview: hostPreview
    ));
    
    return Scaffold(
      body: Stack(
        children: [
          LiveStreamBlurBackgroundImage(),
          // Stream view based on type
          Obx(() {
            switch (controller.liveData.value.type) {
              case LivestreamType.livestream:
                return LivestreamView(
                  streamViews: controller.streamViews,
                  controller: controller
                );
              case LivestreamType.battle:
                return BattleView(
                  isAudience: false,
                  controller: controller
                );
              case LivestreamType.dummy:
                return LivestreamVideoPlayer(
                  controller: controller.videoPlayerController
                );
            }
          }),
          // UI overlay
          KeyboardAvoider(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                LiveStreamHostTopView(controller: controller),
                LiveStreamBottomView(controller: controller),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
```

### Host Publishing Process
```dart
Future<void> startHostPublish() async {
  try {
    String streamID = liveData.value.roomID ?? '';
    
    // Validate host preview
    if (hostPreview == null) {
      showSnackBar('Camera preview not available');
      return;
    }
    
    // Add host stream view
    final hostStreamView = StreamView(
      streamID, 
      liveData.value.hostViewID ?? -1, 
      hostPreview!, 
      false
    );
    streamViews.add(hostStreamView);
    
    // Configure audio
    await zegoEngine.mutePublishStreamAudio(false);
    
    // Start additional services
    startMinViewerTimeoutCheck();
    pushNotificationToFollowers(liveData.value);
    
    // Start publishing
    await zegoEngine.startPublishingStream(streamID);
    
    // Verify stream publishing
    Future.delayed(const Duration(seconds: 1), () {
      _verifyStreamPublishing(streamID);
    });
  } catch (e) {
    Loggers.error('Error starting host publish: $e');
    await zegoEngine.stopPublishingStream();
  }
}
```

---

## 6. Audience Implementation

### Audience Screen Management
```dart
class LiveStreamViewerScreen extends StatefulWidget {
  final List<Livestream> livestreams;
  final int initialIndex;
  
  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.livestreams.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (context, index) {
        final isActive = index == _currentIndex;
        return LiveStreamAudienceScreen(
          livestream: widget.livestreams[index],
          isHost: false,
          isActive: isActive,
        );
      },
    );
  }
}
```

### Stream Switching Logic
```dart
void _onPageChanged(int index) {
  if (_currentIndex != index) {
    final oldIndex = _currentIndex;
    _currentIndex = index;
    
    // Activate new stream
    _activateStreamAtIndex(index);
    
    // Deactivate previous stream
    _deactivateStreamAtIndex(oldIndex);
    
    if (mounted) setState(() {});
  }
}

void _activateStreamAtIndex(int index) {
  if (index >= 0 && index < widget.livestreams.length) {
    final livestream = widget.livestreams[index];
    final roomID = livestream.roomID;
    
    if (roomID != null) {
      _currentActiveRoomID = roomID;
      _createControllerForIndex(index, isActive: true);
      _preCreateAdjacentControllers();
    }
  }
}
```

---

## 7. Stream Management

### Stream View Management
```dart
class LivestreamView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final views = List<StreamView>.from(streamViews);
      
      // Prioritize host stream
      final hostIndex = views.indexWhere((v) => v.streamId == hostId);
      if (hostIndex != -1 && hostIndex != 0) {
        final hostView = views.removeAt(hostIndex);
        views.insert(0, hostView);
      }
      
      // Render based on participant count
      return switch (views.length) {
        1 => LiveStreamUserView(streamingView: views.first),
        2 => OneAndTwoUserView(streamViews: views),
        3 => ThreeUserView(streamViews: views),
        4 => FourUserView(streamViews: views),
        _ => const SizedBox(),
      };
    });
  }
}
```

---

## 8. Real-time Features

### Firebase Integration for Real-time State
```dart
void listenLiveStreamData() {
  liveStreamDocListener = liveStreamDocRef
      .withConverter<Livestream>(
        fromFirestore: (snapshot, _) => Livestream.fromJson(snapshot.data()!),
        toFirestore: (livestream, _) => livestream.toJson(),
      )
      .snapshots()
      .listen((snapshot) {
    if (snapshot.exists) {
      Livestream updatedStream = snapshot.data()!;
      liveData.value = updatedStream;
      
      // Handle stream end
      if (updatedStream.watchingCount == -1) {
        streamEnded();
      }
    }
  });
}
```

### Real-time Comments System
```dart
void fetchLiveStreamComments() {
  liveStreamCommentsListener = liveStreamCommentsRef
      .withConverter<LivestreamComment>(
        fromFirestore: (snapshot, _) => LivestreamComment.fromJson(snapshot.data()!),
        toFirestore: (comment, _) => comment.toJson(),
      )
      .orderBy('created_at', descending: false)
      .snapshots()
      .listen((snapshot) {
    for (var change in snapshot.docChanges) {
      final comment = change.doc.data();
      if (comment == null) continue;
      
      switch (change.type) {
        case DocumentChangeType.added:
          comments.insert(0, comment);
          _handleSpecialComments(comment);
          break;
        case DocumentChangeType.modified:
          // Update existing comment
          break;
        case DocumentChangeType.removed:
          comments.removeWhere((c) => c.id == comment.id);
          break;
      }
    }
  });
}
```

---

## 9. Battle Mode Implementation

### Battle System Architecture
```dart
class BattleView extends StatefulWidget {
  final bool isAudience;
  final LivestreamScreenController controller;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LiveBattleOverlayWidget(controller: controller),
        BattleTimer(controller: controller),
      ],
    );
  }
}
```

### Battle Progress Visualization
```dart
class BuildProgressBar extends StatelessWidget {
  final int red;   // Host coins
  final int blue;  // Co-host coins
  
  @override
  Widget build(BuildContext context) {
    final total = red + blue == 0 ? 1 : red + blue;
    final redWidth = (Get.width * red) / total;
    final blueWidth = (Get.width * blue) / total;
    final alignmentX = ((redWidth / Get.width) * 2) - 1;
    
    return Stack(
      alignment: Alignment.center,
      children: [
        Row(
          children: [
            AnimatedContainer(
              height: 10,
              width: redWidth,
              color: ColorRes.likeRed,
              duration: const Duration(milliseconds: 200),
            ),
            AnimatedContainer(
              height: 10,
              width: blueWidth,
              color: ColorRes.battleProgressColor,
              duration: const Duration(milliseconds: 200),
            ),
          ],
        ),
        // Crown indicator showing current leader
        AnimatedAlign(
          alignment: Alignment(alignmentX, 0),
          duration: const Duration(milliseconds: 200),
          child: Container(
            height: 30,
            width: 30,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Image.asset(AssetRes.icCrown),
          ),
        ),
      ],
    );
  }
}
```

---

## 10. State Management

### Stream State Models
```dart
class LivestreamUserState {
  VideoAudioStatus audioStatus;
  VideoAudioStatus videoStatus;
  LivestreamUserType type;
  int userId;
  int liveCoin;
  int currentBattleCoin;
  int totalBattleCoin;
  List<int> followersGained;
  int joinStreamTime;
  
  // State management methods
  AppUser? getUser(List<AppUser> users);
  int get totalCoin => totalBattleCoin + liveCoin;
}

enum LivestreamUserType {
  host('HOST'),
  coHost('CO-HOST'),
  audience('AUDIENCE'),
  requested('REQUESTED'),
  invited('INVITED'),
  left('LEFT');
}

enum VideoAudioStatus {
  on('ON'),
  offByMe('OFF_BY_ME'),
  offByHost('OFF_BY_HOST');
}
```

---

## 11. Error Handling & Recovery

### Room Login Error Handling
```dart
String _getLoginErrorMessage(int errorCode) {
  switch (errorCode) {
    case 1002001: return 'Room login failed - Invalid room ID';
    case 1002002: return 'Room login failed - Room does not exist';
    case 1002003: return 'Room login failed - User already in room';
    case 1002004: return 'Room login failed - Room is full';
    case 1002005: return 'Room login failed - Network error';
    case 1002006: return 'Room login failed - Authentication failed';
    case 1002007: return 'Room login failed - Room has ended';
    default: return 'Room login failed - Error code: $errorCode';
  }
}

bool _shouldRetryLogin(int errorCode) {
  return [1002005, 1002003].contains(errorCode); // Network error, already in room
}
```

---

## 12. Performance Optimizations

### Memory Management
```dart
@override
void onClose() {
  super.onClose();
  WakelockPlus.disable();
  timer?.cancel();
  minViewerTimeoutTimer?.cancel();
  videoPlayerController.value?.dispose();
  liveStreamUserStatesListener?.cancel();
  liveStreamCommentsListener?.cancel();
  liveStreamDocListener?.cancel();
  countdownPlayer.dispose();
  winAudioPlayer.dispose();
  stopListenEvent();
  logoutRoom(4);
}
```

### Stream View Optimization
```dart
void _cleanupAllControllers() {
  for (final roomID in _createdControllers.toList()) {
    try {
      final controller = Get.find<LivestreamScreenController>(tag: roomID);
      controller.logoutRoom(5).catchError((error) {
        Loggers.error('Error during logout for room $roomID: $error');
      });
      Get.delete<LivestreamScreenController>(tag: roomID);
    } catch (e) {
      Loggers.warning('Controller not found during cleanup for room: $roomID');
    }
  }
  _createdControllers.clear();
}
```

---

## 13. Security & Privacy

### Permission Management
```dart
Future<bool> requestPermission() async {
  try {
    PermissionStatus microphoneStatus = await Permission.microphone.request();
    if (microphoneStatus != PermissionStatus.granted) {
      return false;
    }
    
    PermissionStatus cameraStatus = await Permission.camera.request();
    if (cameraStatus != PermissionStatus.granted) {
      return false;
    }
    
    return true;
  } catch (error) {
    Loggers.error("Permission request exception: $error");
    return false;
  }
}
```

### Stream Access Control
```dart
Future<void> onVideoRequestSend(Livestream liveData) async {
  if (liveData.isRestrictToJoin == 1) {
    showSnackBar("Host has restricted join requests");
    return;
  }
  
  LivestreamComment comment = LivestreamComment(
    commentType: LivestreamCommentType.request,
    senderId: myUserId,
    senderUser: myUser.value?.appUser,
    createdAt: DateTime.now().millisecondsSinceEpoch,
  );
  
  await liveStreamCommentsRef.add(comment.toJson());
}
```

---

## Conclusion

The ZEGO live streaming implementation in Ratulive represents a comprehensive, production-ready solution that handles:

### Key Strengths
- **Robust Architecture**: Clean separation between host and audience functionality
- **Real-time Synchronization**: Firebase integration for instant state updates
- **Error Recovery**: Comprehensive error handling and retry mechanisms
- **Performance Optimization**: Efficient memory management and resource cleanup
- **Scalable Design**: Modular components supporting various streaming scenarios
- **User Experience**: Smooth transitions, intuitive controls, and responsive UI

### Technical Highlights
- **ZEGO SDK Integration**: Professional-grade WebRTC implementation
- **Multi-user Support**: Dynamic layouts for 1-4 participants
- **Battle Mode**: Competitive streaming with real-time coin tracking
- **State Management**: Reactive programming with GetX
- **Firebase Integration**: Real-time database for chat and user states
- **Security**: Permission management and user privacy controls

This implementation demonstrates enterprise-level live streaming capabilities suitable for social media platforms requiring real-time interaction, monetization features, and scalable architecture.
// ⚠️ DO NOT CHANGE ANY KEY

class FirebaseConst {
  static const String users = 'users';
  static const String usersList = 'users_list';
  static const String chats = 'chats';
  static const String messages = 'messages';
  static const String id = 'id';
  static const String msgCount = 'msg_count';
  static const String lastMsg = 'last_msg';
  static const String lastMsgType = 'last_msg_type';
  static const String noDeleteIds = 'no_delete_ids';
  static const String deletedId = 'deleted_id';
  static const String isDeleted = 'is_deleted';
  static const String requestType = 'request_type';
  static const String chatType = 'chat_type';
  static const String iAmBlocked = 'i_am_blocked';
  static const String iBlocked = 'i_blocked';
  static const String chatUser = 'chat_user';
  static const String storyReplyMessage = 'story_reply_message';

  // Common
  static const String appUsers = 'app_users';

  // LiveStream
  static const String liveStreams = 'livestreams';
  static const String comments = 'comments';
  static const String userState = 'user_state';
  static const String type = 'type';
  static const String liveCoin = 'live_coin';
  static const String followersGained = 'followers_gained';
  static const String totalBattleCoin = 'total_battle_coin';
  static const String currentBattleCoin = 'current_battle_coin';
  static const String videoStatus = 'video_status';
  static const String audioStatus = 'audio_status';
  static const String watchingCount = 'watching_count';
  static const String battleCreatedAt = 'battle_created_at';
  static const String battleDuration = 'battle_duration';
  static const String battleType = 'battle_type';
  static const String coHostIds = 'co-host_ids';
  static const String joinStreamTime = 'join_stream_time';
  static const String likeCount = 'like_count';
  static const String isAudioVideoOffByCoHost = 'is_audio_video_off_by_co_host';
  static const String isAudioVideoOffByHost = 'is_audio_video_off_by_host';
}

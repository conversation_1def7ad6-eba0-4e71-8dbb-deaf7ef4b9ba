# Live Streaming Functionality Test Guide

## Overview
This guide provides comprehensive testing procedures for the live streaming functionality fixes implemented to address:
1. Scroll-related login errors
2. Camera toggle errors during live streaming
3. Camera rotation issues during live streaming

## Pre-Testing Setup

### Requirements
- Two test devices (or one device + emulator)
- Stable internet connection
- Camera and microphone permissions granted
- Test user accounts

### Environment Setup
```bash
# Run the automated tests first
chmod +x test_live_streaming.sh
./test_live_streaming.sh
```

## Test Scenarios

### 1. Streamer Perspective Tests

#### 1.1 Camera Toggle Functionality
**Objective**: Verify camera can be turned on/off without errors

**Steps**:
1. Start a live stream
2. Wait for stream to be active
3. Tap the camera toggle button (video camera icon)
4. Verify camera turns off gracefully
5. Tap the camera toggle button again
6. Verify camera turns on without errors

**Expected Results**:
- ✅ Camera toggles smoothly without crashes
- ✅ Video stream stops/starts appropriately
- ✅ Error messages are user-friendly if permissions are denied
- ✅ State is reverted if operation fails

#### 1.2 Camera Rotation/Flip Functionality
**Objective**: Verify camera can be rotated between front/back cameras

**Steps**:
1. Start a live stream with front camera
2. Tap the camera flip button (flip icon)
3. Verify camera switches to back camera
4. Tap the camera flip button again
5. Verify camera switches back to front camera
6. Try flipping when camera is disabled
7. Try rapid consecutive flips

**Expected Results**:
- ✅ Camera flips smoothly between front and back
- ✅ Video quality remains stable during flip
- ✅ Flip is disabled when camera is off (with appropriate message)
- ✅ Rapid flips are handled gracefully

#### 1.3 Microphone Toggle Functionality
**Objective**: Verify microphone can be muted/unmuted without errors

**Steps**:
1. Start a live stream
2. Tap the microphone toggle button
3. Verify microphone is muted
4. Tap the microphone toggle button again
5. Verify microphone is unmuted

**Expected Results**:
- ✅ Microphone toggles correctly
- ✅ Audio stream stops/starts appropriately
- ✅ Visual indicators update correctly

#### 1.4 Stream Publishing Error Handling
**Objective**: Verify robust error handling during stream publishing

**Steps**:
1. Try starting a stream without camera permissions
2. Try starting a stream with poor network connection
3. Try starting a stream and then disconnect internet briefly
4. Try starting multiple streams simultaneously

**Expected Results**:
- ✅ Clear error messages for permission issues
- ✅ Graceful handling of network issues
- ✅ Proper cleanup on failed operations
- ✅ No crashes or undefined states

### 2. Viewer Perspective Tests

#### 2.1 Scroll Behavior Testing
**Objective**: Verify scrolling doesn't trigger login errors

**Steps**:
1. Open live stream viewer
2. Scroll up and down rapidly through multiple streams
3. Continue scrolling for 30+ seconds
4. Check for any login error messages
5. Verify streams continue playing during scroll

**Expected Results**:
- ✅ No "login failed" errors during scrolling
- ✅ Streams continue playing smoothly
- ✅ No unexpected redirects to login screen
- ✅ Background API calls don't interfere with streaming

#### 2.2 Video Player Error Handling
**Objective**: Verify video player handles errors gracefully

**Steps**:
1. Join a stream with poor network connection
2. Join a stream that ends abruptly
3. Join multiple streams in quick succession
4. Try joining with invalid stream URLs

**Expected Results**:
- ✅ Graceful handling of network issues
- ✅ Clear error messages for connection problems
- ✅ Proper cleanup when streams end
- ✅ No crashes with invalid data

#### 2.3 Co-Host Functionality
**Objective**: Verify co-host camera controls work properly

**Steps**:
1. Join as co-host
2. Test camera toggle as co-host
3. Test camera flip as co-host
4. Test microphone toggle as co-host
5. Leave co-host mode

**Expected Results**:
- ✅ Co-host controls appear correctly
- ✅ Camera operations work for co-hosts
- ✅ Proper permissions and restrictions
- ✅ Clean exit from co-host mode

### 3. Edge Case Testing

#### 3.1 Permission Scenarios
**Steps**:
1. Revoke camera permission during streaming
2. Revoke microphone permission during streaming
3. Grant permissions after denial
4. Test with partial permissions

**Expected Results**:
- ✅ Graceful handling of permission changes
- ✅ Clear instructions for granting permissions
- ✅ Proper recovery when permissions are granted

#### 3.2 Network Scenarios
**Steps**:
1. Start stream with WiFi, switch to mobile data
2. Test with very poor network connection
3. Test with intermittent connectivity
4. Test with complete network loss and recovery

**Expected Results**:
- ✅ Smooth transition between network types
- ✅ Appropriate buffering/loading indicators
- ✅ Graceful degradation with poor connection
- ✅ Recovery when connection improves

#### 3.3 Concurrent Operations
**Steps**:
1. Rapidly toggle camera and microphone
2. Flip camera while toggling video
3. Join/leave co-host mode rapidly
4. Perform operations during stream transitions

**Expected Results**:
- ✅ No race conditions or crashes
- ✅ Operations are queued or handled appropriately
- ✅ Consistent final state after operations

## Verification Checklist

### ✅ Fixed Issues
- [ ] Scroll-related login errors eliminated
- [ ] Camera toggle works without errors
- [ ] Camera rotation functions properly
- [ ] Microphone toggle is reliable
- [ ] Stream publishing is robust
- [ ] Error messages are user-friendly
- [ ] State recovery works on failures

### ✅ Performance Checks
- [ ] No memory leaks during camera operations
- [ ] Smooth video playback during state changes
- [ ] Responsive UI during streaming operations
- [ ] Efficient error handling without blocking UI

### ✅ User Experience
- [ ] Clear feedback for all operations
- [ ] Intuitive error messages
- [ ] Consistent behavior across devices
- [ ] Accessible controls and indicators

## Reporting Issues

If any tests fail, please report with:
1. Device information (model, OS version)
2. Exact steps to reproduce
3. Expected vs actual behavior
4. Screenshots/videos if applicable
5. Console logs if available

## Success Criteria

All tests should pass with:
- ✅ No crashes or undefined states
- ✅ Clear, helpful error messages
- ✅ Smooth user experience
- ✅ Proper error recovery
- ✅ Consistent behavior across scenarios

## Additional Notes

- Test on both Android and iOS if possible
- Test with different network conditions
- Test with various device orientations
- Verify accessibility features work correctly
- Check for any performance regressions
